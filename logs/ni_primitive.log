[30173] Create PrimitiveInfo {"group": 0, "mask": 3}
[30402] Create PrimitiveInfo {"group": 0, "mask": 1}
[30415] Create PrimitiveInfo {"noc.type": 2, "noc.wd": 4, "noc.rem_dim0": 0, "noc.size_dim0b": 16, "noc.size_dim1": 32, "noc.size_dim2": 1, "noc.stride_dim1": 512, "noc.stride_dim2": 0, "noc.base_addr": 0, "noc.dest_idx": 1, "group": 0, "mask": 1}
[30578] Create PrimitiveInfo {"group": 0, "mask": 2}
[30591] Create PrimitiveInfo {"noc.type": 2, "noc.wd": 4, "noc.rem_dim0": 0, "noc.size_dim0b": 16, "noc.size_dim1": 32, "noc.size_dim2": 1, "noc.stride_dim1": 512, "noc.stride_dim2": 0, "noc.base_addr": 0, "noc.src_idx": 0, "group": 0, "mask": 2}
[30706] Create PrimitiveInfo {"group": 0, "mask": 2}
