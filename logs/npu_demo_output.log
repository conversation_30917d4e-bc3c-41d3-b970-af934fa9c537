Server '/data/users/jxchen/.mosim/sockets/npu.sock' is listening for incoming connections...
GOLDEN_VM_SYSTEM initialized with 16 cores

Importing 16 tensors from ./in_out/inout_data/input_tensor.json
============================================================

Importing tensor: in1_0_0
  Core ID: 0
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_0_1
  Core ID: 1
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9844, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_0_2
  Core ID: 2
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_0_3
  Core ID: 3
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9922, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_1_0
  Core ID: 4
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9922, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_1_1
  Core ID: 5
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_1_2
  Core ID: 6
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9844, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_1_3
  Core ID: 7
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_2_0
  Core ID: 8
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_2_1
  Core ID: 9
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_2_2
  Core ID: 10
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_2_3
  Core ID: 11
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9922, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_3_0
  Core ID: 12
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_3_1
  Core ID: 13
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_3_2
  Core ID: 14
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9922, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_3_3
  Core ID: 15
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

============================================================
Data import completed
at 2025-08-04 09:52:17.974576 npu running ...
Connection from fd=6
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000075dd00000008
ipcSocketServer receive request data: 00000000000075dd
* * * * Request Simulate to 30173 * * * *
How many requests in queue? - 0
cur_cycle: 0 , next_cycle: 30173
[Subsystem::handle] Received handshake request @sim_time= 30173
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000075dd0000000c
ipcSocketServer receive request data: 00e7b00b0000000000000003
* * * * Request Simulate to 30173 * * * *
How many requests in queue? - 0
cur_cycle: 30173 , next_cycle: 30173
[Subsystem::handle] Received nice instruction request @sim_time= 30173
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000075e800000008
ipcSocketServer receive request data: 00000000000075e8
* * * * Request Simulate to 30184 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 0, 3)
Verifying, current_cycle:30173 - sychronizing_npus:False - target_sim_cycle:30184
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 0, PrimName.GROUP_MASK, group/mask: 0/3, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30173 , next_cycle: 30184
dispatching: Pid: 0, PrimName.GROUP_MASK, group/mask: 0/3, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30184
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000075e80000000c
ipcSocketServer receive request data: 02e7b00b0005000000000012
* * * * Request Simulate to 30184 * * * *
How many requests in queue? - 0
cur_cycle: 30184 , next_cycle: 30184
[Subsystem::handle] Received nice instruction request @sim_time= 30184
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000075f300000008
ipcSocketServer receive request data: 00000000000075f3
* * * * Request Simulate to 30195 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 327680, 18)
ddd, is_drv=False
cur_cycle: 30184 , next_cycle: 30195
[Subsystem::handle] Received handshake request @sim_time= 30195
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000075f30000000c
ipcSocketServer receive request data: 02e7b00b0005000100000400
* * * * Request Simulate to 30195 * * * *
How many requests in queue? - 0
cur_cycle: 30195 , next_cycle: 30195
[Subsystem::handle] Received nice instruction request @sim_time= 30195
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000075fe00000008
ipcSocketServer receive request data: 00000000000075fe
* * * * Request Simulate to 30206 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 327681, 1024)
ddd, is_drv=False
cur_cycle: 30195 , next_cycle: 30206
[Subsystem::handle] Received handshake request @sim_time= 30206
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000075fe0000000c
ipcSocketServer receive request data: 02e7b00b0005000200002020
* * * * Request Simulate to 30206 * * * *
How many requests in queue? - 0
cur_cycle: 30206 , next_cycle: 30206
[Subsystem::handle] Received nice instruction request @sim_time= 30206
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000760900000008
ipcSocketServer receive request data: 0000000000007609
* * * * Request Simulate to 30217 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 327682, 8224)
ddd, is_drv=False
cur_cycle: 30206 , next_cycle: 30217
[Subsystem::handle] Received handshake request @sim_time= 30217
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076090000000c
ipcSocketServer receive request data: 02e7b00b0005000300000010
* * * * Request Simulate to 30217 * * * *
How many requests in queue? - 0
cur_cycle: 30217 , next_cycle: 30217
[Subsystem::handle] Received nice instruction request @sim_time= 30217
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000761400000008
ipcSocketServer receive request data: 0000000000007614
* * * * Request Simulate to 30228 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 327683, 16)
ddd, is_drv=False
cur_cycle: 30217 , next_cycle: 30228
[Subsystem::handle] Received handshake request @sim_time= 30228
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076140000000c
ipcSocketServer receive request data: 02e7b00b0005000300000200
* * * * Request Simulate to 30228 * * * *
How many requests in queue? - 0
cur_cycle: 30228 , next_cycle: 30228
[Subsystem::handle] Received nice instruction request @sim_time= 30228
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000076c200000008
ipcSocketServer receive request data: 00000000000076c2
* * * * Request Simulate to 30402 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 327683, 512)
ddd, is_drv=False
cur_cycle: 30228 , next_cycle: 30402
[Subsystem::handle] Received handshake request @sim_time= 30402
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076c20000000c
ipcSocketServer receive request data: 00e7b00b0000000000000001
* * * * Request Simulate to 30402 * * * *
How many requests in queue? - 0
cur_cycle: 30402 , next_cycle: 30402
[Subsystem::handle] Received nice instruction request @sim_time= 30402
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000076cf00000008
ipcSocketServer receive request data: 00000000000076cf
* * * * Request Simulate to 30415 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 0, 1)
Verifying, current_cycle:30402 - sychronizing_npus:False - target_sim_cycle:30415
Group:0 - Mask:[True, True, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 1, PrimName.GROUP_MASK, group/mask: 0/1, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30402 , next_cycle: 30415
dispatching: Pid: 1, PrimName.GROUP_MASK, group/mask: 0/1, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30415
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076cf0000000c
ipcSocketServer receive request data: 20e7b00b0000000000000001
* * * * Request Simulate to 30415 * * * *
How many requests in queue? - 0
cur_cycle: 30415 , next_cycle: 30415
[Subsystem::handle] Received nice instruction request @sim_time= 30415
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000777200000008
ipcSocketServer receive request data: 0000000000007772
* * * * Request Simulate to 30578 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle noc_src_drv(20e7b00b, 0, 1)
Verifying, current_cycle:30415 - sychronizing_npus:False - target_sim_cycle:30578
Group:0 - Mask:[True, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 2, PrimName.NOC_SRC×1, None, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (16384, 0), size: (256, 32, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
[NoC] Handling PrimName.NOC_SRC primitive 2
      Source: Group None ID None
      Dest: Group [0, 0, 0, 0] ID 1
[NoC] No matching pair found, added to pending list (size: 1)
cur_cycle: 30415 , next_cycle: 30578
dispatching: Pid: 2, PrimName.NOC_SRC×1, None, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30578
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077720000000c
ipcSocketServer receive request data: 00e7b00b0000000000000002
* * * * Request Simulate to 30578 * * * *
How many requests in queue? - 0
cur_cycle: 30578 , next_cycle: 30578
[Subsystem::handle] Received nice instruction request @sim_time= 30578
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000777f00000008
ipcSocketServer receive request data: 000000000000777f
* * * * Request Simulate to 30591 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 0, 2)
Verifying, current_cycle:30578 - sychronizing_npus:False - target_sim_cycle:30591
Group:0 - Mask:[True, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 3, PrimName.GROUP_MASK, group/mask: 0/2, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30578 , next_cycle: 30591
dispatching: Pid: 3, PrimName.GROUP_MASK, group/mask: 0/2, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30591
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000777f0000000c
ipcSocketServer receive request data: 22e7b00b0000000000000000
* * * * Request Simulate to 30591 * * * *
How many requests in queue? - 0
cur_cycle: 30591 , next_cycle: 30591
[Subsystem::handle] Received nice instruction request @sim_time= 30591
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000077f200000008
ipcSocketServer receive request data: 00000000000077f2
* * * * Request Simulate to 30706 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle noc_dest_drv(22e7b00b, 0, 0)
Verifying, current_cycle:30591 - sychronizing_npus:False - target_sim_cycle:30706
Group:0 - Mask:[False, True, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 4, PrimName.NOC_DEST×1, None, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: byte_base: 0x0, byte_stride: (16384, 0), size: (256, 32, 1), type: BF, width: 16
Verifying conv_settings: Empty Conv Info!
[NoC] Handling PrimName.NOC_DEST primitive 4
      Source: Group [0, 0, 0, 0] ID 0
      Dest: Group None ID None
[NoC] No matching pair found, added to pending list (size: 2)
cur_cycle: 30591 , next_cycle: 30706
dispatching: Pid: 4, PrimName.NOC_DEST×1, None, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30706
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077f20000000c
ipcSocketServer receive request data: 0a00478b0000000000000000
* * * * Request Simulate to 30706 * * * *
How many requests in queue? - 0
cur_cycle: 30706 , next_cycle: 30706
[Subsystem::handle] Received nice instruction request @sim_time= 30706
----------------------------------------------------
ipcSocketServer receive request header: 0500000000000077f200000000
* * * * Request Simulate to 30706 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle sync_drv(0a00478b, 0, 0)
Verifying, current_cycle:30706 - sychronizing_npus:False - target_sim_cycle:30706
Group:0 - Mask:[False, True, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 5, PrimName.SYNC×1, None, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30706 , next_cycle: 