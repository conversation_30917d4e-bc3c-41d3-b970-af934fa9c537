              Copyright Ⓒ 2023-2025 by Simango Inc,
              ALL RIGHTS RESERVED
           Mosim Core 2.0.0 (429bae2@master) --- Jul  4 2025 16:25:05
           Mosim 2.0.0 (67fec1f5@master) --- Jul  4 2025 16:25:05
[08-04 09:52:16.521] [0 s] loaded library 'libmosim.so'.
[08-04 09:52:16.530] [0 s] loaded library 'libmosim_common_model.so'.
[08-04 09:52:16.560] [0 s] loaded library 'libn900_vnice.so'.
[08-04 09:52:16.599] [0 s] monitor: ---- start telnet service port 7070 ----
top set global quantum sc_time 2 ns
profiling: image not found: ./fw/demo_vnice.elf
/data/users/jxchen/mosim_workspace/mosim_bin/mosim/3rd/nuclei/bin/objdump: './fw/demo_vnice.elf': No such file
[08-04 09:52:17.780] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-04 09:52:19.181] [17650 ns] Nuclei SDK Build Time: Aug  4 2025, 09:41:55

[08-04 09:52:20.418] [33534 ns] Download Mode: ILM

[08-04 09:52:21.234] [44198 ns] CPU Frequency 162921 Hz

[08-04 09:52:22.058] [54566 ns] CPU HartID: 0

[08-04 09:52:22.524] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.525] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30173
[08-04 09:52:22.525] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.525] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.525] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.525] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.527] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.528] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30184
[08-04 09:52:22.529] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.529] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.529] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.529] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.530] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.531] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30195
[08-04 09:52:22.531] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.531] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.531] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.531] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.533] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.534] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30206
[08-04 09:52:22.534] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.534] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.534] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.534] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.536] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.537] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30217
[08-04 09:52:22.537] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.537] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.537] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.537] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.539] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.540] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30228
[08-04 09:52:22.540] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.540] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.540] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.540] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.569] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.569] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30402
[08-04 09:52:22.569] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.569] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.569] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.569] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.571] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.572] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 30415
[08-04 09:52:22.572] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.572] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.572] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.572] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.599] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.600] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30578
[08-04 09:52:22.600] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.600] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.600] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.600] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.602] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.603] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 30591
[08-04 09:52:22.603] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.603] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.603] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.603] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.622] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 30706
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30706, check_time is: 61412000
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61412000
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61412000
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61412000
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 1410096114, check_time is: 2820192228000
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-04 09:52:22.623] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61412000
[08-04 09:52:22.625] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:52:22.626] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 30717
[08-04 09:52:22.626] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:52:22.626] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:52:22.626] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:52:22.626] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:52:22.627] [61436 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 61436000
[08-04 09:52:22.736] [62682 ns] All tests finished
[08-04 09:52:23.010] [65918 ns]           
[08-04 09:52:37.900] [301642 ns] monitor: client 0.0.0.0:41824 connected close!

Info: /OSCI/SystemC: Simulation stopped by user.
Elapsed time: 21.3396 s
7082.29 cycles/s
