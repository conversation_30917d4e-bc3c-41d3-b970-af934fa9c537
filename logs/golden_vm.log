[GOLDEN_VM_SYSTEM] data_import(json_file_path=./in_out/inout_data/input_tensor.json, data_dir=./in_out/inout_data/in_data/, use_pt_format=True)
[GOLDEN_VM_SYSTEM] write_tensor(core_id=0, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=1, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=2, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=3, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=4, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=5, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=6, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=7, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=8, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=9, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=10, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=11, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=12, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=13, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=14, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] write_tensor(core_id=15, tensor_in=Tensor(shape=[1, 1, 256], dtype=torch.bfloat16), tensor_desc=TensorDescriptor(addr=0x00000000, shape=(1, 1, 256), byte_stride=(512, 512, 32), dtype=BF16))
[GOLDEN_VM_SYSTEM] data_export(json_file_path=./in_out/inout_data/output_tensor.json, data_dir=./in_out/inout_data/out_data/, use_pt_format=True)
