[08-04 11:09:21.845] [0 s] loaded library 'libmosim.so'.
[08-04 11:09:21.853] [0 s] loaded library 'libmosim_common_model.so'.
[08-04 11:09:21.884] [0 s] loaded library 'libn900_vnice.so'.
[08-04 11:09:21.932] [0 s] device: uart init_region:
[08-04 11:09:21.932] [0 s] region   addr               size              
[08-04 11:09:21.932] [0 s] 0x0 0x0 0x41c
[08-04 11:09:21.944] [0 s] monitor: ---- start telnet service port 7070 ----
[08-04 11:09:21.946] [0 s] device: acc0 init_region:
[08-04 11:09:21.946] [0 s] region   addr               size              
[08-04 11:09:21.946] [0 s] 0x0 0x0 0x10000
[08-04 11:09:21.946] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[08-04 11:09:21.946] [0 s] device: acc1 init_region:
[08-04 11:09:21.946] [0 s] region   addr               size              
[08-04 11:09:21.946] [0 s] 0x0 0x0 0x1000
[08-04 11:09:21.946] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[08-04 11:09:21.946] [0 s] device: ddr init_region:
[08-04 11:09:21.946] [0 s] region   addr               size              
[08-04 11:09:22.512] [0 s] 0x0 0x0 0x40000000
[08-04 11:09:22.512] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[08-04 11:09:22.512] [0 s] device: gpio init_region:
[08-04 11:09:22.512] [0 s] region   addr               size              
[08-04 11:09:22.512] [0 s] 0x0 0x0 0x1000
[08-04 11:09:22.512] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[08-04 11:09:22.512] [0 s] device: hole init_region:
[08-04 11:09:22.512] [0 s] region   addr               size              
[08-04 11:09:22.629] [0 s] 0x0 0x0 0xfee0000
[08-04 11:09:22.629] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[08-04 11:09:22.629] [0 s] device: mrom init_region:
[08-04 11:09:22.629] [0 s] region   addr               size              
[08-04 11:09:22.629] [0 s] 0x0 0x0 0x20000
[08-04 11:09:22.629] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[08-04 11:09:22.675] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[08-04 11:09:22.675] [0 s] device: qspi0 init_region:
[08-04 11:09:22.675] [0 s] region   addr               size              
[08-04 11:09:22.675] [0 s] 0x0 0x0 0x1000
[08-04 11:09:22.675] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[08-04 11:09:22.675] [0 s] device: qspi1 init_region:
[08-04 11:09:22.675] [0 s] region   addr               size              
[08-04 11:09:22.781] [0 s] 0x0 0x0 0xf000000
[08-04 11:09:22.781] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[08-04 11:09:22.781] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[08-04 11:09:22.781] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[08-04 11:09:22.781] [0 s] device: xip init_region:
[08-04 11:09:22.781] [0 s] region   addr               size              
[08-04 11:09:23.135] [0 s] 0x0 0x0 0x20000000
[08-04 11:09:23.135] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[08-04 11:09:23.135] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 11:09:23.135] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 11:09:23.135] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 11:09:23.135] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 11:09:23.135] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 11:09:23.135] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 11:09:23.135] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[08-04 11:09:23.269] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[08-04 11:09:23.274] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[08-04 11:09:23.274] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x1f98
[08-04 11:09:23.274] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe00
[08-04 11:09:23.274] [40 ns] n900_vnice transport_dbg: addr: 0x100e00 ,len: 0x800
[08-04 11:09:23.274] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 11:09:23.274] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 11:09:23.274] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 11:09:23.274] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 11:09:23.274] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 11:09:23.274] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 11:09:23.274] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[08-04 11:09:23.276] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[08-04 11:09:24.375] [14645 ns] monitor: client 0.0.0.0:40722 connected
[08-04 11:09:24.420] [15180 ns] monitor get_sc_timestamp
[08-04 11:09:27.388] [51280 ns] monitor get_sc_timestamp
[08-04 11:09:30.389] [62436 ns] monitor get_sc_timestamp
[08-04 11:09:33.408] [62436 ns] monitor get_sc_timestamp
[08-04 11:09:36.391] [62436 ns] monitor get_sc_timestamp
[08-04 11:09:39.380] [62436 ns] monitor get_sc_timestamp
[08-04 11:09:42.388] [62436 ns] monitor get_sc_timestamp
[08-04 11:09:45.398] [62436 ns] monitor get_sc_timestamp
[08-04 11:09:48.391] [62436 ns] monitor get_sc_timestamp
[08-04 11:09:51.457] [62436 ns] monitor get_sc_timestamp
[08-04 11:09:54.440] [62436 ns] monitor get_sc_timestamp
[08-04 11:09:57.437] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:00.438] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:03.436] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:06.438] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:09.434] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:12.443] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:15.455] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:18.456] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:21.453] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:24.455] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:27.454] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:30.457] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:33.451] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:36.455] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:39.456] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:42.453] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:45.458] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:48.451] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:51.457] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:54.455] [62436 ns] monitor get_sc_timestamp
[08-04 11:10:57.452] [62436 ns] monitor get_sc_timestamp
[08-04 11:11:00.457] [62436 ns] monitor get_sc_timestamp
[08-04 11:11:03.455] [62436 ns] monitor get_sc_timestamp
[08-04 11:11:06.460] [62436 ns] monitor get_sc_timestamp
[08-04 11:11:09.466] [62436 ns] monitor get_sc_timestamp
[08-04 11:11:12.465] [62436 ns] monitor get_sc_timestamp
