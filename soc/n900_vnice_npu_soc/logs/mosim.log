[08-04 10:43:16.170] [0 s] loaded library 'libmosim.so'.
[08-04 10:43:16.187] [0 s] loaded library 'libmosim_common_model.so'.
[08-04 10:43:16.222] [0 s] loaded library 'libn900_vnice.so'.
[08-04 10:43:16.273] [0 s] device: uart init_region:
[08-04 10:43:16.273] [0 s] region   addr               size              
[08-04 10:43:16.273] [0 s] 0x0 0x0 0x41c
[08-04 10:43:16.280] [0 s] monitor: ---- start telnet service port 7070 ----
[08-04 10:43:16.281] [0 s] device: acc0 init_region:
[08-04 10:43:16.281] [0 s] region   addr               size              
[08-04 10:43:16.281] [0 s] 0x0 0x0 0x10000
[08-04 10:43:16.281] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[08-04 10:43:16.281] [0 s] device: acc1 init_region:
[08-04 10:43:16.281] [0 s] region   addr               size              
[08-04 10:43:16.281] [0 s] 0x0 0x0 0x1000
[08-04 10:43:16.281] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[08-04 10:43:16.282] [0 s] device: ddr init_region:
[08-04 10:43:16.282] [0 s] region   addr               size              
[08-04 10:43:16.734] [0 s] 0x0 0x0 0x40000000
[08-04 10:43:16.734] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[08-04 10:43:16.734] [0 s] device: gpio init_region:
[08-04 10:43:16.734] [0 s] region   addr               size              
[08-04 10:43:16.734] [0 s] 0x0 0x0 0x1000
[08-04 10:43:16.734] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[08-04 10:43:16.734] [0 s] device: hole init_region:
[08-04 10:43:16.734] [0 s] region   addr               size              
[08-04 10:43:16.850] [0 s] 0x0 0x0 0xfee0000
[08-04 10:43:16.850] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[08-04 10:43:16.850] [0 s] device: mrom init_region:
[08-04 10:43:16.850] [0 s] region   addr               size              
[08-04 10:43:16.850] [0 s] 0x0 0x0 0x20000
[08-04 10:43:16.850] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[08-04 10:43:16.896] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[08-04 10:43:16.896] [0 s] device: qspi0 init_region:
[08-04 10:43:16.896] [0 s] region   addr               size              
[08-04 10:43:16.896] [0 s] 0x0 0x0 0x1000
[08-04 10:43:16.896] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[08-04 10:43:16.896] [0 s] device: qspi1 init_region:
[08-04 10:43:16.896] [0 s] region   addr               size              
[08-04 10:43:16.999] [0 s] 0x0 0x0 0xf000000
[08-04 10:43:16.999] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[08-04 10:43:16.999] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[08-04 10:43:16.999] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[08-04 10:43:16.999] [0 s] device: xip init_region:
[08-04 10:43:16.999] [0 s] region   addr               size              
[08-04 10:43:17.273] [0 s] 0x0 0x0 0x20000000
[08-04 10:43:17.273] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[08-04 10:43:17.273] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 10:43:17.273] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 10:43:17.273] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 10:43:17.273] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 10:43:17.273] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 10:43:17.273] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 10:43:17.273] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[08-04 10:43:17.430] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[08-04 10:43:17.440] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[08-04 10:43:17.441] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x1f98
[08-04 10:43:17.441] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe00
[08-04 10:43:17.441] [40 ns] n900_vnice transport_dbg: addr: 0x100e00 ,len: 0x800
[08-04 10:43:17.441] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 10:43:17.441] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 10:43:17.441] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 10:43:17.441] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 10:43:17.441] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 10:43:17.441] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 10:43:17.441] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[08-04 10:43:17.442] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[08-04 10:43:18.756] [18072 ns] monitor: client 0.0.0.0:59202 connected
[08-04 10:43:18.800] [18636 ns] monitor get_sc_timestamp
[08-04 10:43:21.726] [55026 ns] monitor get_sc_timestamp
[08-04 10:43:24.716] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:27.737] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:30.741] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:33.738] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:36.743] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:39.737] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:42.823] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:45.743] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:48.742] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:51.741] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:54.740] [62436 ns] monitor get_sc_timestamp
[08-04 10:43:57.745] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:00.740] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:03.743] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:06.746] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:09.738] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:12.755] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:15.745] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:18.742] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:21.747] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:24.746] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:27.747] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:30.747] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:33.748] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:36.748] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:39.746] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:42.748] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:45.752] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:48.746] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:51.754] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:54.752] [62436 ns] monitor get_sc_timestamp
[08-04 10:44:57.751] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:00.749] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:03.742] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:06.752] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:09.749] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:12.854] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:15.749] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:18.749] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:21.751] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:24.751] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:27.751] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:30.746] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:33.753] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:36.754] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:39.753] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:42.745] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:45.747] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:48.762] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:51.754] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:54.752] [62436 ns] monitor get_sc_timestamp
[08-04 10:45:57.749] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:00.746] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:03.752] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:06.815] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:09.820] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:12.821] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:15.820] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:18.815] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:21.816] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:37.407] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:37.514] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:40.357] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:43.396] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:46.406] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:49.401] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:52.531] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:55.413] [62436 ns] monitor get_sc_timestamp
[08-04 10:46:58.419] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:01.361] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:04.417] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:07.387] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:10.356] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:13.353] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:16.367] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:19.359] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:22.359] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:25.357] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:28.360] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:31.357] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:34.358] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:37.406] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:40.355] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:43.358] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:46.360] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:49.362] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:52.357] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:55.365] [62436 ns] monitor get_sc_timestamp
[08-04 10:47:58.363] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:01.363] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:04.363] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:07.388] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:10.355] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:13.360] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:16.363] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:19.365] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:22.364] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:25.362] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:28.362] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:31.362] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:34.364] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:37.466] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:40.364] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:43.363] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:46.365] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:49.362] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:52.360] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:55.363] [62436 ns] monitor get_sc_timestamp
[08-04 10:48:58.360] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:01.362] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:04.361] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:07.456] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:10.364] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:13.367] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:16.363] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:19.368] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:22.362] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:25.365] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:28.368] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:31.366] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:34.369] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:37.456] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:40.372] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:43.368] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:46.370] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:49.363] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:52.366] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:55.371] [62436 ns] monitor get_sc_timestamp
[08-04 10:49:58.372] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:01.373] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:04.371] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:07.369] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:10.375] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:13.370] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:16.370] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:19.373] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:22.373] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:25.372] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:28.374] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:31.369] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:34.370] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:37.461] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:40.378] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:43.376] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:46.354] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:49.375] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:52.373] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:55.374] [62436 ns] monitor get_sc_timestamp
[08-04 10:50:58.377] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:01.376] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:04.379] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:07.375] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:10.383] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:13.377] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:16.378] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:19.374] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:22.379] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:25.378] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:28.377] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:31.374] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:34.373] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:37.461] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:40.381] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:43.376] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:46.395] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:49.401] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:52.401] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:55.397] [62436 ns] monitor get_sc_timestamp
[08-04 10:51:58.399] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:01.399] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:04.400] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:07.475] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:10.474] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:13.400] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:16.400] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:19.395] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:22.398] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:25.394] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:28.400] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:31.398] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:34.393] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:37.462] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:40.397] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:43.395] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:46.393] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:49.399] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:52.401] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:55.398] [62436 ns] monitor get_sc_timestamp
[08-04 10:52:58.398] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:01.394] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:04.400] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:07.482] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:10.398] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:13.401] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:16.397] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:19.399] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:22.403] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:25.406] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:28.501] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:31.504] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:34.518] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:37.547] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:40.553] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:43.544] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:46.554] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:49.551] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:52.551] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:55.558] [62436 ns] monitor get_sc_timestamp
[08-04 10:53:58.554] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:01.555] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:04.549] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:07.549] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:10.554] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:13.562] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:16.557] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:19.570] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:22.577] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:25.568] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:28.576] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:31.573] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:34.573] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:37.570] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:40.575] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:43.578] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:46.579] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:49.577] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:52.577] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:55.577] [62436 ns] monitor get_sc_timestamp
[08-04 10:54:58.572] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:01.576] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:04.625] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:07.583] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:10.572] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:13.580] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:16.575] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:19.578] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:22.589] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:25.576] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:28.578] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:31.581] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:34.579] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:37.582] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:40.579] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:43.581] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:46.592] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:49.591] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:52.598] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:55.595] [62436 ns] monitor get_sc_timestamp
[08-04 10:55:58.589] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:01.591] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:04.587] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:07.587] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:10.595] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:13.593] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:16.599] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:19.594] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:22.592] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:25.590] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:28.600] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:31.593] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:34.589] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:37.596] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:40.594] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:43.596] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:46.593] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:49.597] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:52.592] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:55.597] [62436 ns] monitor get_sc_timestamp
[08-04 10:56:58.599] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:01.600] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:04.599] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:07.596] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:10.597] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:13.600] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:16.605] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:19.603] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:22.603] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:25.603] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:28.609] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:31.606] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:34.605] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:37.609] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:40.609] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:43.613] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:46.613] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:49.611] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:52.616] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:55.613] [62436 ns] monitor get_sc_timestamp
[08-04 10:57:58.613] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:01.610] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:04.616] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:07.616] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:10.620] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:13.618] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:16.615] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:19.613] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:22.612] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:25.613] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:28.616] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:31.617] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:34.609] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:37.611] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:40.611] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:43.616] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:46.620] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:49.616] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:52.614] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:55.619] [62436 ns] monitor get_sc_timestamp
[08-04 10:58:58.617] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:01.615] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:04.616] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:07.615] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:10.621] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:13.618] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:16.617] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:19.655] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:22.613] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:25.621] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:28.620] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:31.620] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:34.617] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:37.740] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:40.771] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:43.780] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:46.795] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:49.791] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:52.788] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:55.790] [62436 ns] monitor get_sc_timestamp
[08-04 10:59:58.777] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:01.778] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:04.781] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:07.778] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:10.792] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:13.806] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:16.785] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:19.790] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:22.705] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:25.794] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:28.698] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:31.780] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:34.783] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:37.781] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:40.796] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:43.823] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:46.787] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:49.791] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:52.786] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:55.789] [62436 ns] monitor get_sc_timestamp
[08-04 11:00:58.805] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:01.793] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:04.713] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:07.793] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:10.802] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:13.791] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:16.810] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:19.797] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:33.034] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:37.713] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:37.719] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:40.628] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:43.725] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:46.629] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:49.628] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:52.646] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:55.616] [62436 ns] monitor get_sc_timestamp
[08-04 11:01:58.620] [62436 ns] monitor get_sc_timestamp
[08-04 11:02:01.630] [62436 ns] monitor get_sc_timestamp
[08-04 11:02:04.631] [62436 ns] monitor get_sc_timestamp
[08-04 11:02:07.633] [62436 ns] monitor get_sc_timestamp
[08-04 11:02:10.565] [62436 ns] monitor get_sc_timestamp
[08-04 11:02:13.650] [62436 ns] monitor get_sc_timestamp
[08-04 11:02:16.631] [62436 ns] monitor get_sc_timestamp
[08-04 11:02:19.626] [62436 ns] monitor get_sc_timestamp
[08-04 11:02:22.640] [62436 ns] monitor get_sc_timestamp
[08-04 11:02:25.626] [62436 ns] monitor get_sc_timestamp
