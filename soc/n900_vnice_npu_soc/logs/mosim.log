[08-04 13:45:16.662] [0 s] loaded library 'libmosim.so'.
[08-04 13:45:16.670] [0 s] loaded library 'libmosim_common_model.so'.
[08-04 13:45:16.700] [0 s] loaded library 'libn900_vnice.so'.
[08-04 13:45:16.733] [0 s] device: uart init_region:
[08-04 13:45:16.733] [0 s] region   addr               size              
[08-04 13:45:16.733] [0 s] 0x0 0x0 0x41c
[08-04 13:45:16.742] [0 s] monitor: ---- start telnet service port 7070 ----
[08-04 13:45:16.743] [0 s] device: acc0 init_region:
[08-04 13:45:16.743] [0 s] region   addr               size              
[08-04 13:45:16.743] [0 s] 0x0 0x0 0x10000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[08-04 13:45:16.743] [0 s] device: acc1 init_region:
[08-04 13:45:16.743] [0 s] region   addr               size              
[08-04 13:45:16.743] [0 s] 0x0 0x0 0x1000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[08-04 13:45:16.743] [0 s] device: ddr init_region:
[08-04 13:45:16.744] [0 s] region   addr               size              
[08-04 13:45:17.191] [0 s] 0x0 0x0 0x40000000
[08-04 13:45:17.191] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[08-04 13:45:17.191] [0 s] device: gpio init_region:
[08-04 13:45:17.191] [0 s] region   addr               size              
[08-04 13:45:17.191] [0 s] 0x0 0x0 0x1000
[08-04 13:45:17.191] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[08-04 13:45:17.191] [0 s] device: hole init_region:
[08-04 13:45:17.191] [0 s] region   addr               size              
[08-04 13:45:17.306] [0 s] 0x0 0x0 0xfee0000
[08-04 13:45:17.306] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[08-04 13:45:17.306] [0 s] device: mrom init_region:
[08-04 13:45:17.306] [0 s] region   addr               size              
[08-04 13:45:17.306] [0 s] 0x0 0x0 0x20000
[08-04 13:45:17.306] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[08-04 13:45:17.360] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[08-04 13:45:17.360] [0 s] device: qspi0 init_region:
[08-04 13:45:17.360] [0 s] region   addr               size              
[08-04 13:45:17.360] [0 s] 0x0 0x0 0x1000
[08-04 13:45:17.360] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[08-04 13:45:17.360] [0 s] device: qspi1 init_region:
[08-04 13:45:17.360] [0 s] region   addr               size              
[08-04 13:45:17.473] [0 s] 0x0 0x0 0xf000000
[08-04 13:45:17.473] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[08-04 13:45:17.473] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[08-04 13:45:17.473] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[08-04 13:45:17.473] [0 s] device: xip init_region:
[08-04 13:45:17.473] [0 s] region   addr               size              
[08-04 13:45:17.761] [0 s] 0x0 0x0 0x20000000
[08-04 13:45:17.761] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[08-04 13:45:17.761] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 13:45:17.761] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 13:45:17.761] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 13:45:17.761] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 13:45:17.761] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 13:45:17.761] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 13:45:17.761] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[08-04 13:45:17.982] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[08-04 13:45:17.982] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[08-04 13:45:17.982] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[08-04 13:45:17.982] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[08-04 13:45:17.982] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[08-04 13:45:17.982] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[08-04 13:45:17.982] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[08-04 13:45:17.983] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[08-04 13:45:17.983] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[08-04 13:45:17.983] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[08-04 13:45:17.983] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[08-04 13:45:17.983] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[08-04 13:45:17.983] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[08-04 13:45:17.983] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[08-04 13:45:17.993] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[08-04 13:45:17.994] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x1f98
[08-04 13:45:17.994] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe00
[08-04 13:45:17.994] [40 ns] n900_vnice transport_dbg: addr: 0x100e00 ,len: 0x800
[08-04 13:45:17.994] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 13:45:17.994] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 13:45:17.994] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 13:45:17.994] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 13:45:17.994] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 13:45:17.994] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 13:45:17.994] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[08-04 13:45:17.996] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[08-04 13:45:19.221] [14668 ns] monitor: client 0.0.0.0:42726 connected
[08-04 13:45:19.308] [15668 ns] monitor get_sc_timestamp
[08-04 13:45:22.228] [51161 ns] monitor get_sc_timestamp
[08-04 13:45:25.212] [62180 ns] monitor get_sc_timestamp
[08-04 13:45:28.233] [62180 ns] monitor get_sc_timestamp
[08-04 13:45:31.214] [62180 ns] monitor get_sc_timestamp
[08-04 13:45:34.234] [62180 ns] monitor get_sc_timestamp
