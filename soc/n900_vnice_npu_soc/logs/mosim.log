[08-04 13:30:01.286] [0 s] loaded library 'libmosim.so'.
[08-04 13:30:01.303] [0 s] loaded library 'libmosim_common_model.so'.
[08-04 13:30:01.338] [0 s] loaded library 'libn900_vnice.so'.
[08-04 13:30:01.388] [0 s] device: uart init_region:
[08-04 13:30:01.388] [0 s] region   addr               size              
[08-04 13:30:01.388] [0 s] 0x0 0x0 0x41c
[08-04 13:30:01.401] [0 s] monitor: ---- start telnet service port 7070 ----
[08-04 13:30:01.402] [0 s] device: acc0 init_region:
[08-04 13:30:01.402] [0 s] region   addr               size              
[08-04 13:30:01.403] [0 s] 0x0 0x0 0x10000
[08-04 13:30:01.403] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[08-04 13:30:01.403] [0 s] device: acc1 init_region:
[08-04 13:30:01.403] [0 s] region   addr               size              
[08-04 13:30:01.403] [0 s] 0x0 0x0 0x1000
[08-04 13:30:01.403] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[08-04 13:30:01.403] [0 s] device: ddr init_region:
[08-04 13:30:01.403] [0 s] region   addr               size              
[08-04 13:30:02.087] [0 s] 0x0 0x0 0x40000000
[08-04 13:30:02.087] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[08-04 13:30:02.087] [0 s] device: gpio init_region:
[08-04 13:30:02.087] [0 s] region   addr               size              
[08-04 13:30:02.087] [0 s] 0x0 0x0 0x1000
[08-04 13:30:02.087] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[08-04 13:30:02.087] [0 s] device: hole init_region:
[08-04 13:30:02.087] [0 s] region   addr               size              
[08-04 13:30:02.203] [0 s] 0x0 0x0 0xfee0000
[08-04 13:30:02.203] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[08-04 13:30:02.203] [0 s] device: mrom init_region:
[08-04 13:30:02.203] [0 s] region   addr               size              
[08-04 13:30:02.203] [0 s] 0x0 0x0 0x20000
[08-04 13:30:02.203] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[08-04 13:30:02.260] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[08-04 13:30:02.260] [0 s] device: qspi0 init_region:
[08-04 13:30:02.260] [0 s] region   addr               size              
[08-04 13:30:02.260] [0 s] 0x0 0x0 0x1000
[08-04 13:30:02.260] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[08-04 13:30:02.260] [0 s] device: qspi1 init_region:
[08-04 13:30:02.260] [0 s] region   addr               size              
[08-04 13:30:02.422] [0 s] 0x0 0x0 0xf000000
[08-04 13:30:02.422] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[08-04 13:30:02.422] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[08-04 13:30:02.422] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[08-04 13:30:02.422] [0 s] device: xip init_region:
[08-04 13:30:02.422] [0 s] region   addr               size              
[08-04 13:30:02.651] [0 s] 0x0 0x0 0x20000000
[08-04 13:30:02.651] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[08-04 13:30:02.651] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 13:30:02.651] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 13:30:02.651] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 13:30:02.651] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 13:30:02.651] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 13:30:02.651] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 13:30:02.651] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[08-04 13:30:02.775] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[08-04 13:30:02.775] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x1f98
[08-04 13:30:02.775] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe00
[08-04 13:30:02.775] [40 ns] n900_vnice transport_dbg: addr: 0x100e00 ,len: 0x800
[08-04 13:30:02.775] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 13:30:02.775] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 13:30:02.775] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 13:30:02.775] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 13:30:02.775] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 13:30:02.775] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 13:30:02.775] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[08-04 13:30:02.777] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[08-04 13:30:03.927] [14790 ns] monitor: client 0.0.0.0:46192 connected
[08-04 13:30:03.981] [15464 ns] monitor get_sc_timestamp
[08-04 13:30:06.918] [52493 ns] monitor get_sc_timestamp
[08-04 13:30:09.921] [97994 ns] monitor get_sc_timestamp
[08-04 13:30:12.921] [148428 ns] monitor get_sc_timestamp
[08-04 13:30:15.920] [199249 ns] monitor get_sc_timestamp
[08-04 13:30:18.907] [250734 ns] monitor get_sc_timestamp
[08-04 13:30:21.921] [302684 ns] monitor get_sc_timestamp
[08-04 13:30:24.921] [354332 ns] monitor get_sc_timestamp
[08-04 13:30:27.918] [406022 ns] monitor get_sc_timestamp
[08-04 13:30:30.921] [457792 ns] monitor get_sc_timestamp
[08-04 13:30:33.922] [509566 ns] monitor get_sc_timestamp
[08-04 13:30:36.920] [561202 ns] monitor get_sc_timestamp
[08-04 13:30:40.015] [617894 ns] monitor get_sc_timestamp
[08-04 13:30:43.015] [674604 ns] monitor get_sc_timestamp
[08-04 13:30:46.014] [727801 ns] monitor get_sc_timestamp
[08-04 13:30:49.018] [780230 ns] monitor get_sc_timestamp
[08-04 13:30:52.053] [833387 ns] monitor get_sc_timestamp
[08-04 13:30:55.115] [887032 ns] monitor get_sc_timestamp
[08-04 13:30:58.118] [939554 ns] monitor get_sc_timestamp
[08-04 13:31:01.118] [992050 ns] monitor get_sc_timestamp
[08-04 13:31:04.124] [1044764 ns] monitor get_sc_timestamp
[08-04 13:31:07.120] [1100994 ns] monitor get_sc_timestamp
[08-04 13:31:10.115] [1156812 ns] monitor get_sc_timestamp
[08-04 13:31:13.099] [1213154 ns] monitor get_sc_timestamp
[08-04 13:31:16.116] [1270150 ns] monitor get_sc_timestamp
[08-04 13:31:19.114] [1326598 ns] monitor get_sc_timestamp
[08-04 13:31:28.490] [1503482 ns] monitor get_sc_timestamp
[08-04 13:31:28.492] [1503523 ns] monitor get_sc_timestamp
[08-04 13:31:31.473] [1559702 ns] monitor get_sc_timestamp
[08-04 13:31:34.491] [1616718 ns] monitor get_sc_timestamp
[08-04 13:31:35.861] [1642574 ns] monitor: client 0.0.0.0:46192 connected close!
[08-04 13:31:35.931] [1643302 ns] monitor: client 127.0.0.1:40736 connected
[08-04 13:31:35.972] [1643302 ns] monitor get_sc_timestamp
