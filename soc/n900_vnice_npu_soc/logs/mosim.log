[08-04 13:39:13.922] [0 s] loaded library 'libmosim.so'.
[08-04 13:39:13.930] [0 s] loaded library 'libmosim_common_model.so'.
[08-04 13:39:13.960] [0 s] loaded library 'libn900_vnice.so'.
[08-04 13:39:13.991] [0 s] device: uart init_region:
[08-04 13:39:13.991] [0 s] region   addr               size              
[08-04 13:39:13.991] [0 s] 0x0 0x0 0x41c
[08-04 13:39:14.000] [0 s] monitor: ---- start telnet service port 7070 ----
[08-04 13:39:14.001] [0 s] device: acc0 init_region:
[08-04 13:39:14.001] [0 s] region   addr               size              
[08-04 13:39:14.001] [0 s] 0x0 0x0 0x10000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[08-04 13:39:14.001] [0 s] device: acc1 init_region:
[08-04 13:39:14.001] [0 s] region   addr               size              
[08-04 13:39:14.001] [0 s] 0x0 0x0 0x1000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[08-04 13:39:14.001] [0 s] device: ddr init_region:
[08-04 13:39:14.001] [0 s] region   addr               size              
[08-04 13:39:14.677] [0 s] 0x0 0x0 0x40000000
[08-04 13:39:14.677] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[08-04 13:39:14.677] [0 s] device: gpio init_region:
[08-04 13:39:14.677] [0 s] region   addr               size              
[08-04 13:39:14.677] [0 s] 0x0 0x0 0x1000
[08-04 13:39:14.677] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[08-04 13:39:14.677] [0 s] device: hole init_region:
[08-04 13:39:14.677] [0 s] region   addr               size              
[08-04 13:39:14.878] [0 s] 0x0 0x0 0xfee0000
[08-04 13:39:14.878] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[08-04 13:39:14.878] [0 s] device: mrom init_region:
[08-04 13:39:14.878] [0 s] region   addr               size              
[08-04 13:39:14.878] [0 s] 0x0 0x0 0x20000
[08-04 13:39:14.878] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[08-04 13:39:14.991] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[08-04 13:39:14.991] [0 s] device: qspi0 init_region:
[08-04 13:39:14.991] [0 s] region   addr               size              
[08-04 13:39:14.991] [0 s] 0x0 0x0 0x1000
[08-04 13:39:14.991] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[08-04 13:39:14.991] [0 s] device: qspi1 init_region:
[08-04 13:39:14.991] [0 s] region   addr               size              
[08-04 13:39:15.178] [0 s] 0x0 0x0 0xf000000
[08-04 13:39:15.178] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[08-04 13:39:15.178] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[08-04 13:39:15.178] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[08-04 13:39:15.178] [0 s] device: xip init_region:
[08-04 13:39:15.178] [0 s] region   addr               size              
[08-04 13:39:15.490] [0 s] 0x0 0x0 0x20000000
[08-04 13:39:15.490] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[08-04 13:39:15.490] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 13:39:15.490] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 13:39:15.490] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 13:39:15.490] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 13:39:15.490] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 13:39:15.490] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 13:39:15.490] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[08-04 13:39:15.615] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[08-04 13:39:15.615] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[08-04 13:39:15.615] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[08-04 13:39:15.620] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[08-04 13:39:15.621] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x1f98
[08-04 13:39:15.621] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe00
[08-04 13:39:15.621] [40 ns] n900_vnice transport_dbg: addr: 0x100e00 ,len: 0x800
[08-04 13:39:15.621] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 13:39:15.621] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 13:39:15.621] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 13:39:15.621] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 13:39:15.621] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 13:39:15.621] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 13:39:15.621] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[08-04 13:39:15.622] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[08-04 13:39:16.438] [19082 ns] monitor: client 0.0.0.0:32850 connected
[08-04 13:39:16.480] [20024 ns] monitor get_sc_timestamp
[08-04 13:39:19.417] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:22.414] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:25.438] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:28.464] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:31.432] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:34.433] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:37.455] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:40.465] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:43.455] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:46.457] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:49.461] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:52.454] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:55.457] [62436 ns] monitor get_sc_timestamp
[08-04 13:39:58.499] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:01.484] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:04.484] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:07.490] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:10.493] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:13.493] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:16.492] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:19.495] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:22.492] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:25.495] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:28.539] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:31.579] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:34.583] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:37.581] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:40.576] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:43.581] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:46.586] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:49.577] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:52.582] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:55.575] [62436 ns] monitor get_sc_timestamp
[08-04 13:40:58.583] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:01.583] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:04.575] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:07.580] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:10.576] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:13.585] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:16.580] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:19.574] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:22.584] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:25.585] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:28.583] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:31.581] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:34.576] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:37.582] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:40.580] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:43.582] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:46.583] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:49.580] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:52.580] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:55.587] [62436 ns] monitor get_sc_timestamp
[08-04 13:41:58.583] [62436 ns] monitor get_sc_timestamp
