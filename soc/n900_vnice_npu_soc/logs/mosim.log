[08-04 09:55:22.593] [0 s] loaded library 'libmosim.so'.
[08-04 09:55:22.613] [0 s] loaded library 'libmosim_common_model.so'.
[08-04 09:55:22.648] [0 s] loaded library 'libn900_vnice.so'.
[08-04 09:55:22.695] [0 s] device: uart init_region:
[08-04 09:55:22.695] [0 s] region   addr               size              
[08-04 09:55:22.695] [0 s] 0x0 0x0 0x41c
[08-04 09:55:22.707] [0 s] monitor: ---- start telnet service port 7070 ----
[08-04 09:55:22.708] [0 s] device: acc0 init_region:
[08-04 09:55:22.708] [0 s] region   addr               size              
[08-04 09:55:22.708] [0 s] 0x0 0x0 0x10000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[08-04 09:55:22.709] [0 s] device: acc1 init_region:
[08-04 09:55:22.709] [0 s] region   addr               size              
[08-04 09:55:22.709] [0 s] 0x0 0x0 0x1000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[08-04 09:55:22.709] [0 s] device: ddr init_region:
[08-04 09:55:22.709] [0 s] region   addr               size              
[08-04 09:55:23.184] [0 s] 0x0 0x0 0x40000000
[08-04 09:55:23.184] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[08-04 09:55:23.184] [0 s] device: gpio init_region:
[08-04 09:55:23.184] [0 s] region   addr               size              
[08-04 09:55:23.184] [0 s] 0x0 0x0 0x1000
[08-04 09:55:23.184] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[08-04 09:55:23.184] [0 s] device: hole init_region:
[08-04 09:55:23.184] [0 s] region   addr               size              
[08-04 09:55:23.300] [0 s] 0x0 0x0 0xfee0000
[08-04 09:55:23.300] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[08-04 09:55:23.300] [0 s] device: mrom init_region:
[08-04 09:55:23.300] [0 s] region   addr               size              
[08-04 09:55:23.300] [0 s] 0x0 0x0 0x20000
[08-04 09:55:23.300] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[08-04 09:55:23.346] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[08-04 09:55:23.346] [0 s] device: qspi0 init_region:
[08-04 09:55:23.346] [0 s] region   addr               size              
[08-04 09:55:23.346] [0 s] 0x0 0x0 0x1000
[08-04 09:55:23.346] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[08-04 09:55:23.346] [0 s] device: qspi1 init_region:
[08-04 09:55:23.346] [0 s] region   addr               size              
[08-04 09:55:23.449] [0 s] 0x0 0x0 0xf000000
[08-04 09:55:23.449] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[08-04 09:55:23.449] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[08-04 09:55:23.449] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[08-04 09:55:23.449] [0 s] device: xip init_region:
[08-04 09:55:23.449] [0 s] region   addr               size              
[08-04 09:55:23.725] [0 s] 0x0 0x0 0x20000000
[08-04 09:55:23.725] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[08-04 09:55:23.725] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 09:55:23.725] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 09:55:23.725] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 09:55:23.725] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 09:55:23.725] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 09:55:23.725] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 09:55:23.725] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[08-04 09:55:23.876] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[08-04 09:55:23.877] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x1f98
[08-04 09:55:23.877] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe00
[08-04 09:55:23.877] [40 ns] n900_vnice transport_dbg: addr: 0x100e00 ,len: 0x800
[08-04 09:55:23.877] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-04 09:55:23.877] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-04 09:55:23.877] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-04 09:55:23.877] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-04 09:55:23.877] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-04 09:55:23.877] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-04 09:55:23.877] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[08-04 09:55:23.878] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[08-04 09:55:25.196] [30144 ns] monitor: client 0.0.0.0:48816 connected
[08-04 09:55:25.240] [31128 ns] monitor get_sc_timestamp
[08-04 09:55:28.190] [74238 ns] monitor get_sc_timestamp
[08-04 09:55:31.190] [124864 ns] monitor get_sc_timestamp
[08-04 09:55:34.189] [175748 ns] monitor get_sc_timestamp
[08-04 09:55:37.167] [225636 ns] monitor get_sc_timestamp
[08-04 09:55:40.189] [274831 ns] monitor get_sc_timestamp
[08-04 09:55:43.194] [323658 ns] monitor get_sc_timestamp
[08-04 09:55:46.190] [372562 ns] monitor get_sc_timestamp
[08-04 09:55:49.189] [421652 ns] monitor get_sc_timestamp
[08-04 09:55:52.191] [470826 ns] monitor get_sc_timestamp
[08-04 09:55:55.192] [520146 ns] monitor get_sc_timestamp
[08-04 09:55:58.192] [569520 ns] monitor get_sc_timestamp
[08-04 09:56:01.194] [618667 ns] monitor get_sc_timestamp
[08-04 09:56:04.193] [668104 ns] monitor get_sc_timestamp
[08-04 09:56:07.203] [718606 ns] monitor get_sc_timestamp
[08-04 09:56:08.943] [747898 ns] monitor: client 0.0.0.0:48816 connected close!
[08-04 09:56:09.008] [748592 ns] monitor: client 127.0.0.1:51932 connected
[08-04 09:56:09.056] [748592 ns] monitor get_sc_timestamp
