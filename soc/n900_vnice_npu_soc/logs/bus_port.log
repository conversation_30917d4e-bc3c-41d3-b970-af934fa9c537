[08-04 13:39:13.992] [0 s] axi-master n900_vnice_npu_soc.n900_vnice.mem__128_axi_m binding device n900_vnice_npu_soc.bus.s_0
[08-04 13:39:13.994] [0 s] nice-master n900_vnice_npu_soc.n900_vnice.nice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.nice
[08-04 13:39:13.996] [0 s] vnice-master n900_vnice_npu_soc.n900_vnice.vnice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.vnice
[08-04 13:39:14.000] [0 s] axi-slave stub n900_vnice_npu_soc.n900_vnice.Vn900_core_rams port n900_vnice_npu_soc.n900_vnice.slv__64_axi_s
[08-04 13:39:14.001] [0 s] ------- load n900_vnice_npu_soc.bus route rules: -------
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_0: slave0 to master0 n900_vnice_npu_soc.acc0 addr_range: start 0x100000 -- end 0x1fffff, length 0x100000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_1: slave0 to master1 n900_vnice_npu_soc.mrom addr_range: start 0x0 -- end 0xfffff, length 0x100000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_2: slave0 to master2 n900_vnice_npu_soc.gpio addr_range: start 0x10012000 -- end 0x10012fff, length 0x1000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_3: slave0 to master3 n900_vnice_npu_soc.uart addr_range: start 0x10013000 -- end 0x10013fff, length 0x1000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_4: slave0 to master4 n900_vnice_npu_soc.qspi0 addr_range: start 0x10014000 -- end 0x10023fff, length 0x10000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_5: slave0 to master5 n900_vnice_npu_soc.qspi1 addr_range: start 0x60000000 -- end 0x6effffff, length 0xf000000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_6: slave0 to master6 n900_vnice_npu_soc.hole addr_range: start 0x50000000 -- end 0x5fedffff, length 0xfee0000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_7: slave0 to master7 n900_vnice_npu_soc.xip addr_range: start 0x20000000 -- end 0x3fffffff, length 0x20000000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_8: slave0 to master8 n900_vnice_npu_soc.ddr addr_range: start 0x70180000 -- end 0x9017ffff, length 0x20000000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_9: slave0 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_10: slave0 to master10 n900_vnice_npu_soc.n900_vnice addr_range: start 0x70000000 -- end 0x7017ffff, length 0x180000
[08-04 13:39:14.001] [0 s] n900_vnice_npu_soc.bus rule_11: slave1 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[08-04 13:39:15.615] [0 s] acc0 ------ load_images -------
[08-04 13:39:15.615] [0 s] acc1 ------ load_images -------
[08-04 13:39:15.615] [0 s] bus ------ load_images -------
[08-04 13:39:15.615] [0 s] ddr ------ load_images -------
[08-04 13:39:15.615] [0 s] gpio ------ load_images -------
[08-04 13:39:15.616] [0 s] hole ------ load_images -------
[08-04 13:39:15.616] [0 s] mrom ------ load_images -------
[08-04 13:39:15.616] [0 s] n900_vnice ------ load_images -------
[08-04 13:39:15.616] [0 s] nice_remote_adapter ------ load_images -------
[08-04 13:39:15.616] [0 s] qspi0 ------ load_images -------
[08-04 13:39:15.616] [0 s] qspi1 ------ load_images -------
[08-04 13:39:15.616] [0 s] xip ------ load_images -------
[08-04 13:39:15.620] [40 ns] mem__128_axi_m_0_bridge ------ load_images -------
[08-04 13:39:15.620] [40 ns] load_images: ./fw/operator_work.elf offset 0x0
[08-04 13:39:15.621] [40 ns] load_elf: file ./fw/operator_work.elf with 3 segments
[08-04 13:39:15.621] [40 ns] load_elf: segment 0x70000000         .. 0x70001f97        
[08-04 13:39:15.621] [40 ns] load_elf: segment 0x70100000         .. 0x70100dff        
[08-04 13:39:15.621] [40 ns] load_elf: segment 0x70100e00         .. 0x701015ff        
[08-04 13:39:15.621] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:15.868] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e4403c0
[08-04 13:39:15.868] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:15.868] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:15.868] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:15.868] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013018, tr: 0x7fe09e0c4ea0, reqid: 0
[08-04 13:39:15.868] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013018, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09e4403c0
[08-04 13:39:15.868] [6022 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x18 data 0x0 len 0x4
[08-04 13:39:15.869] [6022 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x18 len 0x4  in pv mode
[08-04 13:39:15.869] [6022 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013018 data 0x0 len 0x4
[08-04 13:39:15.869] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 0
[08-04 13:39:15.869] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:15.869] [6024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:15.869] [6024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:15.870] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013008, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 0
[08-04 13:39:15.870] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:15.870] [6048 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x8 data 0x9e0005e0 len 0x4
[08-04 13:39:15.870] [6048 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x8 len 0x4  in pv mode
[08-04 13:39:15.870] [6048 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013008 data 0x0 len 0x4
[08-04 13:39:15.870] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 0
[08-04 13:39:15.870] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:15.870] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:15.870] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:15.870] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:15.870] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:15.870] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013008, tr: 0x7fe09e0c4ea0, reqid: 1
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013008, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x8 data 0x1 len 0x4
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x8 len 0x4  in pv mode
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013008 data 0x1 len 0x4
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 1
[08-04 13:39:15.871] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:15.871] [6078 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:15.871] [6078 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:15.872] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x1001300c, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 1
[08-04 13:39:15.872] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:15.872] [6104 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0xc data 0x9e000d70 len 0x4
[08-04 13:39:15.872] [6104 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0xc len 0x4  in pv mode
[08-04 13:39:15.872] [6104 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x1001300c data 0x0 len 0x4
[08-04 13:39:15.872] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 1
[08-04 13:39:15.872] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:15.872] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:15.872] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:15.872] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:15.872] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:15.872] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 1001300c, tr: 0x7fe09e0c4ea0, reqid: 2
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x1001300c, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0xc data 0x1 len 0x4
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0xc len 0x4  in pv mode
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x1001300c data 0x1 len 0x4
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 2
[08-04 13:39:15.873] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:15.873] [6134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:15.873] [6134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.373] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 2
[08-04 13:39:16.373] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.373] [17622 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfec160 len 0x4
[08-04 13:39:16.373] [17622 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.373] [17622 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.373] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 2
[08-04 13:39:16.373] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.373] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.373] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.373] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.373] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.373] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 3
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4e len 0x4
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4e len 0x4
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 3
[08-04 13:39:16.374] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.374] [17652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.374] [17652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.383] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 3
[08-04 13:39:16.383] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.383] [17854 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfede60 len 0x4
[08-04 13:39:16.383] [17854 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.383] [17854 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.383] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 3
[08-04 13:39:16.383] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.383] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.383] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.383] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.383] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.383] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 4
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 4
[08-04 13:39:16.385] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.385] [17884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.385] [17884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.394] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 4
[08-04 13:39:16.394] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.394] [18086 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e1224f0 len 0x4
[08-04 13:39:16.394] [18086 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.394] [18086 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.394] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 4
[08-04 13:39:16.394] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.394] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.394] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.394] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.394] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.394] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 5
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63 len 0x4
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x63 len 0x4
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 5
[08-04 13:39:16.395] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.395] [18116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.395] [18116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.404] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 5
[08-04 13:39:16.404] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.404] [18318 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e1241f0 len 0x4
[08-04 13:39:16.404] [18318 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.404] [18318 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.404] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 5
[08-04 13:39:16.404] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.404] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.404] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.404] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.404] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.404] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 6
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 6
[08-04 13:39:16.405] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.405] [18348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.405] [18348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.414] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 6
[08-04 13:39:16.414] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.414] [18550 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e125ef0 len 0x4
[08-04 13:39:16.414] [18550 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.414] [18550 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.414] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 6
[08-04 13:39:16.414] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.414] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.414] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.414] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.414] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.414] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 7
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 7
[08-04 13:39:16.415] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.416] [18580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.416] [18580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.425] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 7
[08-04 13:39:16.425] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.425] [18782 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e127bf0 len 0x4
[08-04 13:39:16.425] [18782 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.425] [18782 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.425] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 7
[08-04 13:39:16.425] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.425] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.425] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.425] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.425] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.425] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 8
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 8
[08-04 13:39:16.426] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.426] [18812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.426] [18812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.435] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 8
[08-04 13:39:16.435] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.435] [19014 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e1298f0 len 0x4
[08-04 13:39:16.435] [19014 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.435] [19014 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.435] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 8
[08-04 13:39:16.435] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.435] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.435] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.435] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.435] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.435] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 9
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 9
[08-04 13:39:16.436] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.436] [19044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.436] [19044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.445] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 9
[08-04 13:39:16.445] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.445] [19246 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e12b5f0 len 0x4
[08-04 13:39:16.445] [19246 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.445] [19246 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.445] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 9
[08-04 13:39:16.445] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.445] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.445] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.445] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.445] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.445] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 10
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x53 len 0x4
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x53 len 0x4
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 10
[08-04 13:39:16.447] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.447] [19276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.447] [19276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.456] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 10
[08-04 13:39:16.456] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.456] [19478 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e12d2f0 len 0x4
[08-04 13:39:16.456] [19478 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.456] [19478 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.456] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 10
[08-04 13:39:16.456] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.456] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.456] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.456] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.456] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.456] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 11
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 11
[08-04 13:39:16.457] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.457] [19508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.457] [19508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.466] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 11
[08-04 13:39:16.466] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.466] [19710 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e12eff0 len 0x4
[08-04 13:39:16.466] [19710 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.466] [19710 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.466] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 11
[08-04 13:39:16.466] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.466] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.466] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.466] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.466] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.466] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 12
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4b len 0x4
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4b len 0x4
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 12
[08-04 13:39:16.467] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.467] [19740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.467] [19740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.476] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 12
[08-04 13:39:16.476] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.476] [19942 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e130cf0 len 0x4
[08-04 13:39:16.476] [19942 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.476] [19942 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.476] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 12
[08-04 13:39:16.476] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.476] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.476] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.476] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.476] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.476] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 13
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 13
[08-04 13:39:16.478] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.478] [19972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.478] [19972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.487] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 13
[08-04 13:39:16.487] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.487] [20174 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e1329f0 len 0x4
[08-04 13:39:16.487] [20174 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.487] [20174 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.487] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 13
[08-04 13:39:16.487] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.487] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.487] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.487] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.487] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.487] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 14
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x42 len 0x4
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x42 len 0x4
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 14
[08-04 13:39:16.488] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.488] [20204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.488] [20204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.497] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 14
[08-04 13:39:16.497] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.497] [20406 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e1346f0 len 0x4
[08-04 13:39:16.497] [20406 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.497] [20406 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.497] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 14
[08-04 13:39:16.497] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.497] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.497] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.497] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.497] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.497] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 15
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 15
[08-04 13:39:16.499] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.499] [20436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.499] [20436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.507] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 15
[08-04 13:39:16.507] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.507] [20638 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e1363f0 len 0x4
[08-04 13:39:16.507] [20638 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.507] [20638 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.507] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 15
[08-04 13:39:16.507] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.507] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.507] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.507] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.507] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.507] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 16
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 16
[08-04 13:39:16.509] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.509] [20668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.509] [20668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.518] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 16
[08-04 13:39:16.518] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.518] [20870 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e1380f0 len 0x4
[08-04 13:39:16.518] [20870 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.518] [20870 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.518] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 16
[08-04 13:39:16.518] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.518] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.518] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.518] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.518] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.518] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 17
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 17
[08-04 13:39:16.519] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.519] [20900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.519] [20900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.528] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 17
[08-04 13:39:16.528] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.528] [21102 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e139df0 len 0x4
[08-04 13:39:16.528] [21102 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.528] [21102 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.528] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 17
[08-04 13:39:16.528] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.528] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.528] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.528] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.528] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.528] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 18
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 18
[08-04 13:39:16.529] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.529] [21132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.529] [21132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.538] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 18
[08-04 13:39:16.538] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.538] [21334 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e13baf0 len 0x4
[08-04 13:39:16.538] [21334 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.538] [21334 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.538] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 18
[08-04 13:39:16.538] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.538] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.538] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.538] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.538] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.538] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 19
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 19
[08-04 13:39:16.540] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.540] [21364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.540] [21364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.548] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 19
[08-04 13:39:16.548] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.548] [21566 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e13d7f0 len 0x4
[08-04 13:39:16.548] [21566 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.548] [21566 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.548] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 19
[08-04 13:39:16.548] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.548] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.548] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.548] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.548] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.548] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 20
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x54 len 0x4
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x54 len 0x4
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 20
[08-04 13:39:16.550] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.550] [21596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.550] [21596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.559] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 20
[08-04 13:39:16.559] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.559] [21798 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e13f4f0 len 0x4
[08-04 13:39:16.559] [21798 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.559] [21798 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.559] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 20
[08-04 13:39:16.559] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.559] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.559] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.559] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.559] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.559] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 21
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 21
[08-04 13:39:16.560] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.560] [21828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.560] [21828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.569] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 21
[08-04 13:39:16.569] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.569] [22030 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e1411f0 len 0x4
[08-04 13:39:16.569] [22030 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.569] [22030 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.569] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 21
[08-04 13:39:16.569] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.569] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.569] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.569] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.569] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.569] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 22
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6d len 0x4
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6d len 0x4
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 22
[08-04 13:39:16.570] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.570] [22060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.570] [22060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.579] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 22
[08-04 13:39:16.579] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.579] [22262 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e037330 len 0x4
[08-04 13:39:16.579] [22262 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.579] [22262 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.579] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 22
[08-04 13:39:16.579] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.579] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.579] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.579] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.579] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.579] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 23
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 23
[08-04 13:39:16.581] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.581] [22292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.581] [22292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.590] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 23
[08-04 13:39:16.590] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.590] [22494 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e039030 len 0x4
[08-04 13:39:16.590] [22494 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.590] [22494 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.590] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 23
[08-04 13:39:16.590] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.590] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.590] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.590] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.590] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.590] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 24
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 24
[08-04 13:39:16.591] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.591] [22524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.591] [22524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.600] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 24
[08-04 13:39:16.600] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.600] [22726 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e03ad30 len 0x4
[08-04 13:39:16.600] [22726 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.600] [22726 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.600] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 24
[08-04 13:39:16.600] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.600] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.600] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.600] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.600] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.600] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 25
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 25
[08-04 13:39:16.601] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.601] [22756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.601] [22756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.610] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 25
[08-04 13:39:16.610] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.610] [22958 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e03ca30 len 0x4
[08-04 13:39:16.610] [22958 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.610] [22958 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.610] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 25
[08-04 13:39:16.610] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.610] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.610] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.610] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.610] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.610] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 26
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x41 len 0x4
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x41 len 0x4
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 26
[08-04 13:39:16.611] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.612] [22988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.612] [22988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.620] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 26
[08-04 13:39:16.620] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.620] [23190 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e03e730 len 0x4
[08-04 13:39:16.620] [23190 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.620] [23190 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.620] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 26
[08-04 13:39:16.620] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.620] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.620] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.620] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.620] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.620] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 27
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 27
[08-04 13:39:16.622] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.622] [23220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.622] [23220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.631] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 27
[08-04 13:39:16.631] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.631] [23422 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e040430 len 0x4
[08-04 13:39:16.631] [23422 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.631] [23422 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.631] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 27
[08-04 13:39:16.631] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.631] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.631] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.631] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.631] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.631] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 28
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x67 len 0x4
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x67 len 0x4
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 28
[08-04 13:39:16.632] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.632] [23452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.632] [23452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.641] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 28
[08-04 13:39:16.641] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.641] [23654 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e042130 len 0x4
[08-04 13:39:16.641] [23654 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.641] [23654 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.641] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 28
[08-04 13:39:16.641] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.641] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.641] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.641] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.641] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.641] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 29
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 29
[08-04 13:39:16.642] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.642] [23684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.642] [23684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.651] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 29
[08-04 13:39:16.651] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.651] [23886 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e043e30 len 0x4
[08-04 13:39:16.651] [23886 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.651] [23886 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.651] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 29
[08-04 13:39:16.651] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.651] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.651] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.651] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.651] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.651] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 30
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 30
[08-04 13:39:16.653] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.653] [23916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.653] [23916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.661] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 30
[08-04 13:39:16.661] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.661] [24118 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e045b30 len 0x4
[08-04 13:39:16.661] [24118 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.661] [24118 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.661] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 30
[08-04 13:39:16.661] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.661] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.661] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.661] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.661] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.661] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 31
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x34 len 0x4
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x34 len 0x4
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 31
[08-04 13:39:16.663] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.663] [24148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.663] [24148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.672] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 31
[08-04 13:39:16.672] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.672] [24350 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e047830 len 0x4
[08-04 13:39:16.672] [24350 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.672] [24350 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.672] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 31
[08-04 13:39:16.672] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.672] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.672] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.672] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.672] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.672] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 32
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 32
[08-04 13:39:16.673] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.673] [24380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.673] [24380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.682] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 32
[08-04 13:39:16.682] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.682] [24582 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e049530 len 0x4
[08-04 13:39:16.682] [24582 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.682] [24582 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.682] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 32
[08-04 13:39:16.682] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.682] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.682] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.682] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.682] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.682] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 33
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 33
[08-04 13:39:16.683] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.683] [24612 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.683] [24612 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.692] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 33
[08-04 13:39:16.692] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.692] [24814 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e04b230 len 0x4
[08-04 13:39:16.692] [24814 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.692] [24814 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.692] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 33
[08-04 13:39:16.692] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.692] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.692] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.692] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.692] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.692] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 34
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 34
[08-04 13:39:16.694] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.694] [24844 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.694] [24844 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.703] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 34
[08-04 13:39:16.703] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.703] [25046 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e04cf30 len 0x4
[08-04 13:39:16.703] [25046 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.703] [25046 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.703] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 34
[08-04 13:39:16.703] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.703] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.703] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.703] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.703] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.703] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 35
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 35
[08-04 13:39:16.704] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.704] [25076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.704] [25076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.714] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 35
[08-04 13:39:16.714] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.715] [25278 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e04ec30 len 0x4
[08-04 13:39:16.715] [25278 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.715] [25278 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.715] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 35
[08-04 13:39:16.715] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.715] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.715] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.715] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.715] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.715] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 36
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 36
[08-04 13:39:16.716] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.717] [25308 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.717] [25308 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.728] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 36
[08-04 13:39:16.728] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.728] [25510 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e050930 len 0x4
[08-04 13:39:16.728] [25510 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.728] [25510 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.728] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 36
[08-04 13:39:16.728] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.728] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.728] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.728] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.728] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.728] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 37
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x2c len 0x4
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x2c len 0x4
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 37
[08-04 13:39:16.730] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.730] [25540 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.730] [25540 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.741] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 37
[08-04 13:39:16.741] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.741] [25742 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e052630 len 0x4
[08-04 13:39:16.741] [25742 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.741] [25742 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.741] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 37
[08-04 13:39:16.741] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.741] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.741] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.741] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.741] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.741] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 38
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 38
[08-04 13:39:16.743] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.743] [25772 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.743] [25772 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.754] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 38
[08-04 13:39:16.754] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.754] [25974 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e054330 len 0x4
[08-04 13:39:16.754] [25974 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.754] [25974 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.754] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 38
[08-04 13:39:16.754] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.754] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.754] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.754] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.754] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.754] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 39
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 39
[08-04 13:39:16.756] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.756] [26004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.756] [26004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.767] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 39
[08-04 13:39:16.767] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.767] [26206 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e056030 len 0x4
[08-04 13:39:16.767] [26206 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.767] [26206 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.767] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 39
[08-04 13:39:16.767] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.767] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.767] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.767] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.767] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.767] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 40
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x33 len 0x4
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x33 len 0x4
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 40
[08-04 13:39:16.769] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.769] [26236 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.769] [26236 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.780] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 40
[08-04 13:39:16.780] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.780] [26438 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e057d30 len 0x4
[08-04 13:39:16.780] [26438 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.780] [26438 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.780] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 40
[08-04 13:39:16.780] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.780] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.780] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.780] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.780] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.780] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 41
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 41
[08-04 13:39:16.782] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.782] [26468 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.782] [26468 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.800] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 41
[08-04 13:39:16.800] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.800] [26670 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e059a30 len 0x4
[08-04 13:39:16.800] [26670 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.800] [26670 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.800] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 41
[08-04 13:39:16.800] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.800] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.800] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.800] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.800] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.800] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 42
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 42
[08-04 13:39:16.801] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.801] [26700 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.801] [26700 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.813] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 42
[08-04 13:39:16.813] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.813] [26902 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e05b730 len 0x4
[08-04 13:39:16.813] [26902 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.813] [26902 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.813] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 42
[08-04 13:39:16.813] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.813] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.813] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.813] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.813] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.813] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 43
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 43
[08-04 13:39:16.814] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.815] [26932 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.815] [26932 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.826] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 43
[08-04 13:39:16.826] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.826] [27134 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e05d430 len 0x4
[08-04 13:39:16.826] [27134 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.826] [27134 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.826] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 43
[08-04 13:39:16.826] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.826] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.826] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.826] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.826] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.826] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 44
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 44
[08-04 13:39:16.827] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.827] [27164 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.827] [27164 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.838] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 44
[08-04 13:39:16.838] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.838] [27366 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e05f130 len 0x4
[08-04 13:39:16.838] [27366 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.838] [27366 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.838] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 44
[08-04 13:39:16.838] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.838] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.838] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.838] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.838] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.838] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 45
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x33 len 0x4
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x33 len 0x4
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 45
[08-04 13:39:16.840] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.840] [27396 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.840] [27396 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.851] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 45
[08-04 13:39:16.851] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.851] [27598 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e060e30 len 0x4
[08-04 13:39:16.851] [27598 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.851] [27598 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.851] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 45
[08-04 13:39:16.851] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.851] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.851] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.851] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.851] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.851] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 46
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x37 len 0x4
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x37 len 0x4
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 46
[08-04 13:39:16.852] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.852] [27628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.852] [27628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:16.874] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 46
[08-04 13:39:16.874] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.874] [27830 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9c1722d0 len 0x4
[08-04 13:39:16.874] [27830 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.874] [27830 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.874] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 46
[08-04 13:39:16.874] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.874] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.874] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.874] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.874] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.874] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.876] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.876] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.876] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.876] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.876] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 47
[08-04 13:39:16.876] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.876] [27858 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:39:16.876] [27858 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.877] [27858 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:39:16.877] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 47
[08-04 13:39:16.877] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.877] [27860 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.877] [27860 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.888] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 47
[08-04 13:39:16.888] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.888] [28036 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e3c6640 len 0x4
[08-04 13:39:16.888] [28036 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.888] [28036 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.888] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 47
[08-04 13:39:16.888] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.888] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.888] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.888] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.888] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.888] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 48
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 48
[08-04 13:39:16.890] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.890] [28066 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.890] [28066 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:16.894] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 48
[08-04 13:39:16.894] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:16.894] [28142 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e15b960 len 0x4
[08-04 13:39:16.894] [28142 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.894] [28142 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:16.894] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 48
[08-04 13:39:16.894] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:16.894] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.894] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.894] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.894] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:16.894] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:16.895] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:16.895] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:16.895] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:16.895] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:16.895] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 49
[08-04 13:39:16.895] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:16.895] [28170 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 13:39:16.895] [28170 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:16.896] [28170 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 13:39:16.896] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 49
[08-04 13:39:16.896] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:16.896] [28172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:16.896] [28172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.184] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 49
[08-04 13:39:17.184] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.184] [33506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e023f90 len 0x4
[08-04 13:39:17.184] [33506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.184] [33506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.184] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 49
[08-04 13:39:17.184] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.184] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.184] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.184] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.184] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.184] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 50
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 50
[08-04 13:39:17.186] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.186] [33536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.186] [33536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.197] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 50
[08-04 13:39:17.197] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.197] [33738 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e062990 len 0x4
[08-04 13:39:17.197] [33738 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.197] [33738 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.197] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 50
[08-04 13:39:17.197] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.197] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.197] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.197] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.197] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.197] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.198] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.198] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.198] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.198] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.198] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 51
[08-04 13:39:17.198] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.198] [33766 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 13:39:17.198] [33766 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.199] [33766 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 13:39:17.199] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 51
[08-04 13:39:17.199] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.199] [33768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.199] [33768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.210] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 51
[08-04 13:39:17.210] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.210] [33970 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e064690 len 0x4
[08-04 13:39:17.210] [33970 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.210] [33970 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.210] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 51
[08-04 13:39:17.210] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.210] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.210] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.210] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.210] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.210] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 52
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x77 len 0x4
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x77 len 0x4
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 52
[08-04 13:39:17.211] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.211] [34 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.211] [34 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.222] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 52
[08-04 13:39:17.222] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.222] [34202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e066390 len 0x4
[08-04 13:39:17.222] [34202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.222] [34202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.222] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 52
[08-04 13:39:17.222] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.222] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.222] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.222] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.222] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.222] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 53
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 53
[08-04 13:39:17.224] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.224] [34232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.224] [34232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.235] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 53
[08-04 13:39:17.235] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.235] [34434 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e068090 len 0x4
[08-04 13:39:17.235] [34434 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.235] [34434 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.235] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 53
[08-04 13:39:17.235] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.235] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.235] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.235] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.235] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.235] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 54
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 54
[08-04 13:39:17.236] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.236] [34464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.236] [34464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.247] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 54
[08-04 13:39:17.247] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.247] [34666 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e069d90 len 0x4
[08-04 13:39:17.248] [34666 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.248] [34666 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.248] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 54
[08-04 13:39:17.248] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.248] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.248] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.248] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.248] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.248] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 55
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 55
[08-04 13:39:17.249] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.249] [34696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.249] [34696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.260] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 55
[08-04 13:39:17.260] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.260] [34898 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e06ba90 len 0x4
[08-04 13:39:17.260] [34898 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.260] [34898 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.260] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 55
[08-04 13:39:17.260] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.260] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.260] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.260] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.260] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.260] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 56
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61 len 0x4
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x61 len 0x4
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 56
[08-04 13:39:17.262] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.262] [34928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.262] [34928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.273] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 56
[08-04 13:39:17.273] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.273] [35130 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfe2aa0 len 0x4
[08-04 13:39:17.273] [35130 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.273] [35130 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.273] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 56
[08-04 13:39:17.273] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.273] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.273] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.273] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.273] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.273] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 57
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 57
[08-04 13:39:17.275] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.275] [35160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.275] [35160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.286] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 57
[08-04 13:39:17.286] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.286] [35362 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfe47a0 len 0x4
[08-04 13:39:17.286] [35362 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.286] [35362 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.286] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 57
[08-04 13:39:17.286] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.286] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.286] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.286] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.286] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.286] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 58
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 58
[08-04 13:39:17.287] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.287] [35392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.287] [35392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.298] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 58
[08-04 13:39:17.298] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.298] [35594 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfe64a0 len 0x4
[08-04 13:39:17.298] [35594 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.298] [35594 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.298] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 58
[08-04 13:39:17.298] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.298] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.298] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.298] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.298] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.298] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 59
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 59
[08-04 13:39:17.300] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.300] [35624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.300] [35624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.311] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 59
[08-04 13:39:17.311] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.311] [35826 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfe81a0 len 0x4
[08-04 13:39:17.311] [35826 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.311] [35826 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.311] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 59
[08-04 13:39:17.311] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.311] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.311] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.311] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.311] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.311] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 60
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 60
[08-04 13:39:17.312] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.313] [35856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.313] [35856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.323] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 60
[08-04 13:39:17.323] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.323] [36058 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfe9e80 len 0x4
[08-04 13:39:17.323] [36058 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.323] [36058 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.323] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 60
[08-04 13:39:17.323] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.323] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.323] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.323] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.323] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.323] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 61
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 61
[08-04 13:39:17.325] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.325] [36088 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.325] [36088 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.336] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 61
[08-04 13:39:17.336] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.336] [36290 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfebb80 len 0x4
[08-04 13:39:17.336] [36290 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.336] [36290 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.336] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 61
[08-04 13:39:17.336] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.336] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.336] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.336] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.336] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.336] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 62
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 62
[08-04 13:39:17.338] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.338] [36320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.338] [36320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.349] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 62
[08-04 13:39:17.349] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.349] [36522 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfed880 len 0x4
[08-04 13:39:17.349] [36522 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.349] [36522 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.349] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 62
[08-04 13:39:17.349] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.349] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.349] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.349] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.349] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.349] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 63
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 63
[08-04 13:39:17.350] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.351] [36552 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.351] [36552 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.361] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 63
[08-04 13:39:17.361] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.361] [36754 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfefeb0 len 0x4
[08-04 13:39:17.361] [36754 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.361] [36754 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.361] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 63
[08-04 13:39:17.361] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.361] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.361] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.361] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.361] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.361] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 64
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 64
[08-04 13:39:17.363] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.363] [36784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.363] [36784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.374] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 64
[08-04 13:39:17.374] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.374] [36986 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dff1bb0 len 0x4
[08-04 13:39:17.374] [36986 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.374] [36986 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.374] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 64
[08-04 13:39:17.374] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.374] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.374] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.374] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.374] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.374] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 65
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49 len 0x4
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x49 len 0x4
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 65
[08-04 13:39:17.376] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.376] [37016 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.376] [37016 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.387] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 65
[08-04 13:39:17.387] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.387] [37218 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dff38b0 len 0x4
[08-04 13:39:17.387] [37218 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.387] [37218 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.387] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 65
[08-04 13:39:17.387] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.387] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.387] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.387] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.387] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.387] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.388] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.388] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.388] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.388] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.388] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 66
[08-04 13:39:17.388] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.388] [37246 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4c len 0x4
[08-04 13:39:17.389] [37246 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.389] [37246 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4c len 0x4
[08-04 13:39:17.389] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 66
[08-04 13:39:17.389] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.389] [37248 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.389] [37248 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.400] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 66
[08-04 13:39:17.400] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.400] [37450 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dff55b0 len 0x4
[08-04 13:39:17.400] [37450 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.400] [37450 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.400] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 66
[08-04 13:39:17.400] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.400] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.400] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.400] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.400] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.400] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 67
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 67
[08-04 13:39:17.401] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.401] [37480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.401] [37480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.412] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 67
[08-04 13:39:17.412] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.412] [37682 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dff72b0 len 0x4
[08-04 13:39:17.412] [37682 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.412] [37682 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.412] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 67
[08-04 13:39:17.412] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.412] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.413] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.413] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.413] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.413] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 68
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 68
[08-04 13:39:17.414] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.414] [37712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.414] [37712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.424] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 68
[08-04 13:39:17.424] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.424] [37888 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dff8c70 len 0x4
[08-04 13:39:17.424] [37888 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.424] [37888 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.424] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 68
[08-04 13:39:17.424] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.424] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.424] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.424] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.424] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.424] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 69
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 69
[08-04 13:39:17.425] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.426] [37918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.426] [37918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.430] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 69
[08-04 13:39:17.430] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.430] [37994 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dff99b0 len 0x4
[08-04 13:39:17.430] [37994 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.430] [37994 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.430] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 69
[08-04 13:39:17.430] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.430] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.430] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.430] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.430] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.430] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 70
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 70
[08-04 13:39:17.431] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.432] [38024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.432] [38024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.760] [44170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 70
[08-04 13:39:17.760] [44170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.760] [44170 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e014c30 len 0x4
[08-04 13:39:17.760] [44170 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.760] [44170 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.760] [44170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 70
[08-04 13:39:17.760] [44170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.760] [44170 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.760] [44170 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.760] [44170 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.760] [44170 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.760] [44170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 71
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x43 len 0x4
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x43 len 0x4
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 71
[08-04 13:39:17.761] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.762] [44200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.762] [44200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.773] [44402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 71
[08-04 13:39:17.773] [44402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.773] [44402 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e016910 len 0x4
[08-04 13:39:17.773] [44402 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.773] [44402 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.773] [44402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 71
[08-04 13:39:17.773] [44402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.773] [44402 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.773] [44402 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.773] [44402 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.773] [44402 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.773] [44402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 72
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x50 len 0x4
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x50 len 0x4
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 72
[08-04 13:39:17.774] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.774] [44432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.774] [44432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.785] [44634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 72
[08-04 13:39:17.785] [44634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.785] [44634 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e018610 len 0x4
[08-04 13:39:17.785] [44634 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.785] [44634 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.785] [44634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 72
[08-04 13:39:17.785] [44634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.785] [44634 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.785] [44634 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.785] [44634 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.785] [44634 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.785] [44634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 73
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x55 len 0x4
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x55 len 0x4
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 73
[08-04 13:39:17.787] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.787] [44664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.787] [44664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.798] [44866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 73
[08-04 13:39:17.798] [44866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.798] [44866 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e01a310 len 0x4
[08-04 13:39:17.798] [44866 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.798] [44866 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.798] [44866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 73
[08-04 13:39:17.798] [44866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.798] [44866 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.798] [44866 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.798] [44866 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.798] [44866 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.798] [44866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 74
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 74
[08-04 13:39:17.799] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.800] [44896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.800] [44896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.810] [45098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 74
[08-04 13:39:17.810] [45098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.810] [45098 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e01c010 len 0x4
[08-04 13:39:17.810] [45098 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.810] [45098 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.810] [45098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 74
[08-04 13:39:17.810] [45098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.810] [45098 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.810] [45098 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.810] [45098 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.810] [45098 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.810] [45098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 75
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x46 len 0x4
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x46 len 0x4
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 75
[08-04 13:39:17.812] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.812] [45128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.812] [45128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.823] [45330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 75
[08-04 13:39:17.823] [45330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.823] [45330 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e02b0f0 len 0x4
[08-04 13:39:17.823] [45330 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.823] [45330 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.823] [45330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 75
[08-04 13:39:17.823] [45330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.823] [45330 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.823] [45330 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.823] [45330 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.823] [45330 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.823] [45330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 76
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x72 len 0x4
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x72 len 0x4
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 76
[08-04 13:39:17.825] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.825] [45360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.825] [45360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.836] [45562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 76
[08-04 13:39:17.836] [45562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.836] [45562 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e02cdf0 len 0x4
[08-04 13:39:17.836] [45562 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.836] [45562 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.836] [45562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 76
[08-04 13:39:17.836] [45562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.836] [45562 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.836] [45562 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.836] [45562 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.836] [45562 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.836] [45562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 77
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 77
[08-04 13:39:17.837] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.838] [45592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.838] [45592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.849] [45794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 77
[08-04 13:39:17.849] [45794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.849] [45794 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e02eaf0 len 0x4
[08-04 13:39:17.849] [45794 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.849] [45794 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.849] [45794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 77
[08-04 13:39:17.849] [45794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.849] [45794 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.849] [45794 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.849] [45794 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.849] [45794 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.849] [45794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 78
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x71 len 0x4
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x71 len 0x4
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 78
[08-04 13:39:17.850] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.850] [45824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.850] [45824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.861] [46026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 78
[08-04 13:39:17.861] [46026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.861] [46026 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0307f0 len 0x4
[08-04 13:39:17.861] [46026 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.861] [46026 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.861] [46026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 78
[08-04 13:39:17.861] [46026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.861] [46026 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.861] [46026 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.861] [46026 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.861] [46026 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.861] [46026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 79
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 79
[08-04 13:39:17.862] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.863] [46056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.863] [46056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.873] [46258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 79
[08-04 13:39:17.873] [46258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.873] [46258 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0324f0 len 0x4
[08-04 13:39:17.873] [46258 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.873] [46258 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.873] [46258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 79
[08-04 13:39:17.873] [46258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.873] [46258 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.873] [46258 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.873] [46258 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.873] [46258 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.873] [46258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 80
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 80
[08-04 13:39:17.875] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.875] [46288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.875] [46288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.886] [46490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 80
[08-04 13:39:17.886] [46490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.886] [46490 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0341f0 len 0x4
[08-04 13:39:17.886] [46490 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.886] [46490 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.886] [46490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 80
[08-04 13:39:17.886] [46490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.886] [46490 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.886] [46490 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.886] [46490 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.886] [46490 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.886] [46490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 81
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 81
[08-04 13:39:17.887] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.887] [46520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.887] [46520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.898] [46722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 81
[08-04 13:39:17.898] [46722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.898] [46722 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e035ef0 len 0x4
[08-04 13:39:17.898] [46722 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.898] [46722 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.898] [46722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 81
[08-04 13:39:17.898] [46722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.898] [46722 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.898] [46722 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.898] [46722 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.898] [46722 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.898] [46722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 82
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63 len 0x4
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x63 len 0x4
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 82
[08-04 13:39:17.900] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.900] [46752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.900] [46752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.911] [46954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 82
[08-04 13:39:17.911] [46954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.911] [46954 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e037bf0 len 0x4
[08-04 13:39:17.911] [46954 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.911] [46954 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.911] [46954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 82
[08-04 13:39:17.911] [46954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.911] [46954 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.911] [46954 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.911] [46954 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.911] [46954 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.911] [46954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 83
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x79 len 0x4
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x79 len 0x4
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 83
[08-04 13:39:17.912] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.912] [46984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.912] [46984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.923] [47186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 83
[08-04 13:39:17.923] [47186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.923] [47186 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0398f0 len 0x4
[08-04 13:39:17.923] [47186 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.923] [47186 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.923] [47186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 83
[08-04 13:39:17.923] [47186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.923] [47186 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.923] [47186 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.923] [47186 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.923] [47186 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.923] [47186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 84
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 84
[08-04 13:39:17.925] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.925] [47216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.925] [47216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.942] [47418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 84
[08-04 13:39:17.942] [47418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.942] [47418 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e03b5f0 len 0x4
[08-04 13:39:17.942] [47418 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.942] [47418 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.942] [47418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 84
[08-04 13:39:17.942] [47418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.942] [47418 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.942] [47418 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.942] [47418 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.942] [47418 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.942] [47418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 85
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 85
[08-04 13:39:17.943] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.943] [47448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.943] [47448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.954] [47650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 85
[08-04 13:39:17.954] [47650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.954] [47650 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e03d2f0 len 0x4
[08-04 13:39:17.954] [47650 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.954] [47650 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.954] [47650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 85
[08-04 13:39:17.954] [47650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.954] [47650 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.954] [47650 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.954] [47650 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.954] [47650 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.954] [47650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 86
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x36 len 0x4
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x36 len 0x4
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 86
[08-04 13:39:17.956] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.956] [47680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.956] [47680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.967] [47882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 86
[08-04 13:39:17.967] [47882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.967] [47882 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e03eff0 len 0x4
[08-04 13:39:17.967] [47882 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.967] [47882 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.967] [47882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 86
[08-04 13:39:17.967] [47882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.967] [47882 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.967] [47882 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.967] [47882 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.967] [47882 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.967] [47882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 87
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 87
[08-04 13:39:17.969] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.969] [47912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.969] [47912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:17.980] [48114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 87
[08-04 13:39:17.980] [48114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.980] [48114 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e040cf0 len 0x4
[08-04 13:39:17.980] [48114 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.980] [48114 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.980] [48114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 87
[08-04 13:39:17.980] [48114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.980] [48114 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.980] [48114 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.980] [48114 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.980] [48114 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.980] [48114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 88
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x39 len 0x4
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x39 len 0x4
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 88
[08-04 13:39:17.981] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.981] [48144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.981] [48144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:17.993] [48346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 88
[08-04 13:39:17.993] [48346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:17.993] [48346 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0429f0 len 0x4
[08-04 13:39:17.993] [48346 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.993] [48346 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:17.993] [48346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 88
[08-04 13:39:17.993] [48346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:17.993] [48346 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.993] [48346 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.993] [48346 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.993] [48346 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:17.993] [48346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 89
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 89
[08-04 13:39:17.994] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:17.994] [48376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:17.994] [48376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.005] [48578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 89
[08-04 13:39:18.005] [48578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.005] [48578 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0446f0 len 0x4
[08-04 13:39:18.005] [48578 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.005] [48578 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.005] [48578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 89
[08-04 13:39:18.005] [48578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.005] [48578 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.005] [48578 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.005] [48578 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.005] [48578 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.005] [48578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 90
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 90
[08-04 13:39:18.007] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.007] [48608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.007] [48608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.018] [48810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 90
[08-04 13:39:18.018] [48810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.018] [48810 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0463f0 len 0x4
[08-04 13:39:18.018] [48810 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.018] [48810 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.018] [48810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 90
[08-04 13:39:18.018] [48810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.018] [48810 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.018] [48810 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.018] [48810 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.018] [48810 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.018] [48810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 91
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 91
[08-04 13:39:18.020] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.020] [48840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.020] [48840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.031] [49042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 91
[08-04 13:39:18.031] [49042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.031] [49042 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0480f0 len 0x4
[08-04 13:39:18.031] [49042 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.031] [49042 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.031] [49042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 91
[08-04 13:39:18.031] [49042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.031] [49042 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.031] [49042 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.031] [49042 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.031] [49042 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.031] [49042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 92
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x48 len 0x4
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x48 len 0x4
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 92
[08-04 13:39:18.033] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.033] [49072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.033] [49072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.044] [49274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 92
[08-04 13:39:18.044] [49274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.044] [49274 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e049df0 len 0x4
[08-04 13:39:18.044] [49274 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.044] [49274 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.044] [49274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 92
[08-04 13:39:18.044] [49274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.044] [49274 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.044] [49274 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.044] [49274 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.044] [49274 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.044] [49274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 93
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x7a len 0x4
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x7a len 0x4
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 93
[08-04 13:39:18.046] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.046] [49304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.046] [49304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.057] [49506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 93
[08-04 13:39:18.057] [49506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.057] [49506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e122950 len 0x4
[08-04 13:39:18.057] [49506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.057] [49506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.057] [49506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 93
[08-04 13:39:18.057] [49506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.057] [49506 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.057] [49506 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.057] [49506 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.057] [49506 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.057] [49506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 94
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 94
[08-04 13:39:18.059] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.059] [49536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.059] [49536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.068] [49712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 94
[08-04 13:39:18.068] [49712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.068] [49712 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e124310 len 0x4
[08-04 13:39:18.068] [49712 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.068] [49712 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.068] [49712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 94
[08-04 13:39:18.068] [49712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.068] [49712 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.068] [49712 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.068] [49712 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.068] [49712 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.068] [49712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 95
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 95
[08-04 13:39:18.070] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.070] [49742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.070] [49742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.074] [49818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 95
[08-04 13:39:18.074] [49818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.074] [49818 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e125050 len 0x4
[08-04 13:39:18.074] [49818 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.074] [49818 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.074] [49818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 95
[08-04 13:39:18.074] [49818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.074] [49818 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.074] [49818 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.074] [49818 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.074] [49818 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.074] [49818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 96
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 96
[08-04 13:39:18.076] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.076] [49848 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.076] [49848 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.348] [54538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 96
[08-04 13:39:18.348] [54538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.348] [54538 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e3cb4a0 len 0x4
[08-04 13:39:18.348] [54538 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.348] [54538 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.348] [54538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 96
[08-04 13:39:18.348] [54538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.348] [54538 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.348] [54538 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.348] [54538 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.348] [54538 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.348] [54538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 97
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x43 len 0x4
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x43 len 0x4
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 97
[08-04 13:39:18.350] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.350] [54568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.350] [54568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.361] [54770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 97
[08-04 13:39:18.361] [54770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.361] [54770 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e122f90 len 0x4
[08-04 13:39:18.361] [54770 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.361] [54770 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.361] [54770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 97
[08-04 13:39:18.361] [54770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.361] [54770 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.361] [54770 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.361] [54770 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.361] [54770 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.361] [54770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 98
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x50 len 0x4
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x50 len 0x4
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 98
[08-04 13:39:18.363] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.363] [54800 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.363] [54800 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.374] [55002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 98
[08-04 13:39:18.374] [55002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.374] [55002 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0287e0 len 0x4
[08-04 13:39:18.374] [55002 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.374] [55002 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.374] [55002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 98
[08-04 13:39:18.374] [55002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.374] [55002 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.374] [55002 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.374] [55002 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.374] [55002 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.374] [55002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 99
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x55 len 0x4
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x55 len 0x4
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 99
[08-04 13:39:18.376] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.376] [55032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.376] [55032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.387] [55234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 99
[08-04 13:39:18.387] [55234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.387] [55234 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e02a4e0 len 0x4
[08-04 13:39:18.387] [55234 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.387] [55234 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.387] [55234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 99
[08-04 13:39:18.387] [55234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.387] [55234 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.387] [55234 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.387] [55234 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.387] [55234 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.387] [55234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 100
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 100
[08-04 13:39:18.388] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.389] [55264 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.389] [55264 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.400] [55466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 100
[08-04 13:39:18.400] [55466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.400] [55466 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e026d60 len 0x4
[08-04 13:39:18.400] [55466 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.400] [55466 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.400] [55466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 100
[08-04 13:39:18.400] [55466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.400] [55466 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.400] [55466 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.400] [55466 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.400] [55466 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.400] [55466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 101
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x48 len 0x4
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x48 len 0x4
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 101
[08-04 13:39:18.401] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.401] [55496 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.401] [55496 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.412] [55698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 101
[08-04 13:39:18.412] [55698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.412] [55698 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e3c7e00 len 0x4
[08-04 13:39:18.412] [55698 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.412] [55698 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.412] [55698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 101
[08-04 13:39:18.412] [55698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.412] [55698 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.412] [55698 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.412] [55698 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.412] [55698 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.412] [55698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 102
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61 len 0x4
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x61 len 0x4
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 102
[08-04 13:39:18.414] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.414] [55728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.414] [55728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.425] [55930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 102
[08-04 13:39:18.425] [55930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.425] [55930 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e3c9b00 len 0x4
[08-04 13:39:18.425] [55930 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.425] [55930 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.425] [55930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 102
[08-04 13:39:18.425] [55930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.425] [55930 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.425] [55930 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.425] [55930 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.425] [55930 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.425] [55930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 103
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x72 len 0x4
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x72 len 0x4
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 103
[08-04 13:39:18.427] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.427] [55960 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.427] [55960 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.438] [56162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 103
[08-04 13:39:18.438] [56162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.438] [56162 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9df9f5f0 len 0x4
[08-04 13:39:18.438] [56162 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.438] [56162 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.438] [56162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 103
[08-04 13:39:18.438] [56162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.438] [56162 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.438] [56162 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.438] [56162 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.438] [56162 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.438] [56162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 104
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x74 len 0x4
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x74 len 0x4
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 104
[08-04 13:39:18.440] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.440] [56192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.440] [56192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.451] [56394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 104
[08-04 13:39:18.451] [56394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.451] [56394 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9dfa12f0 len 0x4
[08-04 13:39:18.451] [56394 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.451] [56394 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.451] [56394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 104
[08-04 13:39:18.451] [56394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.451] [56394 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.451] [56394 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.451] [56394 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.451] [56394 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.451] [56394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 105
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49 len 0x4
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x49 len 0x4
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 105
[08-04 13:39:18.453] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.453] [56424 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.453] [56424 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.464] [56626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 105
[08-04 13:39:18.464] [56626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.464] [56626 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9df94d30 len 0x4
[08-04 13:39:18.464] [56626 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.464] [56626 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.464] [56626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 105
[08-04 13:39:18.464] [56626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.464] [56626 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.464] [56626 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.464] [56626 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.464] [56626 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.464] [56626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 106
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 106
[08-04 13:39:18.465] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.466] [56656 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.466] [56656 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.477] [56858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 106
[08-04 13:39:18.477] [56858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.477] [56858 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9df96a30 len 0x4
[08-04 13:39:18.477] [56858 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.477] [56858 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.477] [56858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 106
[08-04 13:39:18.477] [56858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.477] [56858 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.477] [56858 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.477] [56858 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.477] [56858 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.477] [56858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 107
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 107
[08-04 13:39:18.478] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.479] [56888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.479] [56888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.490] [57090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 107
[08-04 13:39:18.490] [57090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.490] [57090 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9df99000 len 0x4
[08-04 13:39:18.490] [57090 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.490] [57090 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.490] [57090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 107
[08-04 13:39:18.490] [57090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.490] [57090 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.490] [57090 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.490] [57090 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.490] [57090 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.490] [57090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 108
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 108
[08-04 13:39:18.491] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.491] [57120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.491] [57120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.503] [57322 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 108
[08-04 13:39:18.503] [57322 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.503] [57322 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9df9ad00 len 0x4
[08-04 13:39:18.503] [57322 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.503] [57322 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.503] [57322 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 108
[08-04 13:39:18.503] [57322 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.503] [57322 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.503] [57322 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.503] [57322 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.503] [57322 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.503] [57322 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 109
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 109
[08-04 13:39:18.504] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.504] [57352 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.504] [57352 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
[08-04 13:39:18.515] [57554 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09e0c4ea0, reqid: 109
[08-04 13:39:18.515] [57554 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.515] [57554 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9df9c9c0 len 0x4
[08-04 13:39:18.515] [57554 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.515] [57554 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.515] [57554 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 109
[08-04 13:39:18.515] [57554 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.515] [57554 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.515] [57554 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.515] [57554 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.515] [57554 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.515] [57554 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 110
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 110
[08-04 13:39:18.517] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.517] [57584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.517] [57584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.527] [57760 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 110
[08-04 13:39:18.527] [57760 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.527] [57760 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0be420 len 0x4
[08-04 13:39:18.527] [57760 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.527] [57760 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.527] [57760 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 110
[08-04 13:39:18.527] [57760 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.527] [57760 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.527] [57760 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.527] [57760 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.527] [57760 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.527] [57760 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09c1757b0, reqid: 111
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09e0c4ea0
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09c1757b0, wt: 0x7fe09e0c4ea0
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 111
[08-04 13:39:18.528] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.528] [57790 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.528] [57790 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09c1757b0
[08-04 13:39:18.532] [57866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fe09c1757b0, reqid: 111
[08-04 13:39:18.532] [57866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:39:18.532] [57866 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x9e0bf160 len 0x4
[08-04 13:39:18.532] [57866 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.532] [57866 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:39:18.532] [57866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 111
[08-04 13:39:18.532] [57866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:39:18.532] [57866 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.532] [57866 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.532] [57866 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.532] [57866 ns] axi2tlm: rdata.write 0x0
[08-04 13:39:18.532] [57866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fe09c1757b0
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fe09e0c4ea0, reqid: 112
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fe09e0c4ea0, wt: 0x7fe09c1757b0
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 112
[08-04 13:39:18.534] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:39:18.534] [57896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:39:18.534] [57896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fe09e0c4ea0
