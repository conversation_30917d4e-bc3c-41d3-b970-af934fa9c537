[08-04 13:45:16.734] [0 s] axi-master n900_vnice_npu_soc.n900_vnice.mem__128_axi_m binding device n900_vnice_npu_soc.bus.s_0
[08-04 13:45:16.736] [0 s] nice-master n900_vnice_npu_soc.n900_vnice.nice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.nice
[08-04 13:45:16.739] [0 s] vnice-master n900_vnice_npu_soc.n900_vnice.vnice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.vnice
[08-04 13:45:16.742] [0 s] axi-slave stub n900_vnice_npu_soc.n900_vnice.Vn900_core_rams port n900_vnice_npu_soc.n900_vnice.slv__64_axi_s
[08-04 13:45:16.743] [0 s] ------- load n900_vnice_npu_soc.bus route rules: -------
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_0: slave0 to master0 n900_vnice_npu_soc.acc0 addr_range: start 0x100000 -- end 0x1fffff, length 0x100000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_1: slave0 to master1 n900_vnice_npu_soc.mrom addr_range: start 0x0 -- end 0xfffff, length 0x100000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_2: slave0 to master2 n900_vnice_npu_soc.gpio addr_range: start 0x10012000 -- end 0x10012fff, length 0x1000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_3: slave0 to master3 n900_vnice_npu_soc.uart addr_range: start 0x10013000 -- end 0x10013fff, length 0x1000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_4: slave0 to master4 n900_vnice_npu_soc.qspi0 addr_range: start 0x10014000 -- end 0x10023fff, length 0x10000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_5: slave0 to master5 n900_vnice_npu_soc.qspi1 addr_range: start 0x60000000 -- end 0x6effffff, length 0xf000000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_6: slave0 to master6 n900_vnice_npu_soc.hole addr_range: start 0x50000000 -- end 0x5fedffff, length 0xfee0000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_7: slave0 to master7 n900_vnice_npu_soc.xip addr_range: start 0x20000000 -- end 0x3fffffff, length 0x20000000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_8: slave0 to master8 n900_vnice_npu_soc.ddr addr_range: start 0x70180000 -- end 0x9017ffff, length 0x20000000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_9: slave0 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_10: slave0 to master10 n900_vnice_npu_soc.n900_vnice addr_range: start 0x70000000 -- end 0x7017ffff, length 0x180000
[08-04 13:45:16.743] [0 s] n900_vnice_npu_soc.bus rule_11: slave1 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[08-04 13:45:17.982] [0 s] acc0 ------ load_images -------
[08-04 13:45:17.982] [0 s] acc1 ------ load_images -------
[08-04 13:45:17.982] [0 s] bus ------ load_images -------
[08-04 13:45:17.982] [0 s] ddr ------ load_images -------
[08-04 13:45:17.982] [0 s] gpio ------ load_images -------
[08-04 13:45:17.982] [0 s] hole ------ load_images -------
[08-04 13:45:17.982] [0 s] mrom ------ load_images -------
[08-04 13:45:17.982] [0 s] n900_vnice ------ load_images -------
[08-04 13:45:17.982] [0 s] nice_remote_adapter ------ load_images -------
[08-04 13:45:17.983] [0 s] qspi0 ------ load_images -------
[08-04 13:45:17.983] [0 s] qspi1 ------ load_images -------
[08-04 13:45:17.983] [0 s] xip ------ load_images -------
[08-04 13:45:17.993] [40 ns] mem__128_axi_m_0_bridge ------ load_images -------
[08-04 13:45:17.994] [40 ns] load_images: ./fw/operator_work.elf offset 0x0
[08-04 13:45:17.994] [40 ns] load_elf: file ./fw/operator_work.elf with 3 segments
[08-04 13:45:17.994] [40 ns] load_elf: segment 0x70000000         .. 0x70001f97        
[08-04 13:45:17.994] [40 ns] load_elf: segment 0x70100000         .. 0x70100dff        
[08-04 13:45:17.994] [40 ns] load_elf: segment 0x70100e00         .. 0x701015ff        
[08-04 13:45:17.994] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:18.439] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a4403c0
[08-04 13:45:18.439] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:18.439] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:18.439] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:18.439] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013018, tr: 0x7fad4a0c4ea0, reqid: 0
[08-04 13:45:18.439] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013018, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad4a4403c0
[08-04 13:45:18.439] [6022 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x18 data 0x0 len 0x4
[08-04 13:45:18.440] [6022 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x18 len 0x4  in pv mode
[08-04 13:45:18.440] [6022 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013018 data 0x0 len 0x4
[08-04 13:45:18.440] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 0
[08-04 13:45:18.440] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:18.440] [6024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:18.440] [6024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:18.442] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013008, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 0
[08-04 13:45:18.442] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:18.442] [6048 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x8 data 0x4a0005e0 len 0x4
[08-04 13:45:18.442] [6048 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x8 len 0x4  in pv mode
[08-04 13:45:18.442] [6048 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013008 data 0x0 len 0x4
[08-04 13:45:18.442] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 0
[08-04 13:45:18.442] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:18.442] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:18.442] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:18.442] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:18.442] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:18.442] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013008, tr: 0x7fad4a0c4ea0, reqid: 1
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013008, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x8 data 0x1 len 0x4
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x8 len 0x4  in pv mode
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013008 data 0x1 len 0x4
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 1
[08-04 13:45:18.444] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:18.444] [6078 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:18.444] [6078 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:18.446] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x1001300c, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 1
[08-04 13:45:18.446] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:18.446] [6104 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0xc data 0x4a000d70 len 0x4
[08-04 13:45:18.446] [6104 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0xc len 0x4  in pv mode
[08-04 13:45:18.446] [6104 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x1001300c data 0x0 len 0x4
[08-04 13:45:18.446] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 1
[08-04 13:45:18.446] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:18.446] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:18.446] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:18.446] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:18.446] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:18.446] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 1001300c, tr: 0x7fad4a0c4ea0, reqid: 2
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x1001300c, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0xc data 0x1 len 0x4
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0xc len 0x4  in pv mode
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x1001300c data 0x1 len 0x4
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 2
[08-04 13:45:18.448] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:18.448] [6134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:18.448] [6134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.472] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 2
[08-04 13:45:19.472] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.472] [17622 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49fec160 len 0x4
[08-04 13:45:19.472] [17622 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.472] [17622 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.472] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 2
[08-04 13:45:19.472] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.472] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.472] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.472] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.472] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.472] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 3
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4e len 0x4
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4e len 0x4
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 3
[08-04 13:45:19.475] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.475] [17652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.475] [17652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.491] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 3
[08-04 13:45:19.491] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.491] [17854 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49fede60 len 0x4
[08-04 13:45:19.491] [17854 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.491] [17854 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.491] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 3
[08-04 13:45:19.491] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.491] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.491] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.491] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.491] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.491] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 4
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 4
[08-04 13:45:19.493] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.494] [17884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.494] [17884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.510] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 4
[08-04 13:45:19.510] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.510] [18086 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a1224f0 len 0x4
[08-04 13:45:19.510] [18086 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.510] [18086 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.510] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 4
[08-04 13:45:19.510] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.510] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.510] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.510] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.510] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.510] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 5
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63 len 0x4
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x63 len 0x4
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 5
[08-04 13:45:19.512] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.512] [18116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.512] [18116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.529] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 5
[08-04 13:45:19.529] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.529] [18318 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a1241f0 len 0x4
[08-04 13:45:19.529] [18318 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.529] [18318 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.529] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 5
[08-04 13:45:19.529] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.529] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.529] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.529] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.529] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.529] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 6
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 6
[08-04 13:45:19.531] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.531] [18348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.531] [18348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.548] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 6
[08-04 13:45:19.548] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.548] [18550 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a125ef0 len 0x4
[08-04 13:45:19.548] [18550 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.548] [18550 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.548] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 6
[08-04 13:45:19.548] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.548] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.548] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.548] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.548] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.548] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 7
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 7
[08-04 13:45:19.550] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.550] [18580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.550] [18580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.567] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 7
[08-04 13:45:19.567] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.567] [18782 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a127bf0 len 0x4
[08-04 13:45:19.567] [18782 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.567] [18782 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.567] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 7
[08-04 13:45:19.567] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.567] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.567] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.567] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.567] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.567] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 8
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 8
[08-04 13:45:19.569] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.569] [18812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.569] [18812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.585] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 8
[08-04 13:45:19.585] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.585] [19014 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a1298f0 len 0x4
[08-04 13:45:19.585] [19014 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.585] [19014 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.585] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 8
[08-04 13:45:19.585] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.585] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.585] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.585] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.585] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.585] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 9
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 9
[08-04 13:45:19.588] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.588] [19044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.588] [19044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.604] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 9
[08-04 13:45:19.604] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.604] [19246 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a12b5f0 len 0x4
[08-04 13:45:19.604] [19246 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.604] [19246 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.604] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 9
[08-04 13:45:19.604] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.604] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.604] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.604] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.604] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.604] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 10
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x53 len 0x4
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x53 len 0x4
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 10
[08-04 13:45:19.606] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.607] [19276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.607] [19276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.623] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 10
[08-04 13:45:19.623] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.623] [19478 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a12d2f0 len 0x4
[08-04 13:45:19.623] [19478 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.623] [19478 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.623] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 10
[08-04 13:45:19.623] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.623] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.623] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.623] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.623] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.623] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 11
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 11
[08-04 13:45:19.625] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.626] [19508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.626] [19508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.642] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 11
[08-04 13:45:19.642] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.642] [19710 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a12eff0 len 0x4
[08-04 13:45:19.642] [19710 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.642] [19710 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.642] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 11
[08-04 13:45:19.642] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.642] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.642] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.642] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.642] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.642] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 12
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4b len 0x4
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4b len 0x4
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 12
[08-04 13:45:19.644] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.644] [19740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.644] [19740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.661] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 12
[08-04 13:45:19.661] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.661] [19942 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a130cf0 len 0x4
[08-04 13:45:19.661] [19942 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.661] [19942 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.661] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 12
[08-04 13:45:19.661] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.661] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.661] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.661] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.661] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.661] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 13
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 13
[08-04 13:45:19.663] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.663] [19972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.663] [19972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.679] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 13
[08-04 13:45:19.679] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.679] [20174 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a1329f0 len 0x4
[08-04 13:45:19.679] [20174 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.679] [20174 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.679] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 13
[08-04 13:45:19.679] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.679] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.679] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.679] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.679] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.679] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 14
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x42 len 0x4
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x42 len 0x4
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 14
[08-04 13:45:19.682] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.682] [20204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.682] [20204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.698] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 14
[08-04 13:45:19.698] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.698] [20406 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a1346f0 len 0x4
[08-04 13:45:19.698] [20406 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.698] [20406 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.698] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 14
[08-04 13:45:19.698] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.698] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.698] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.698] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.698] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.698] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 15
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 15
[08-04 13:45:19.700] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.701] [20436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.701] [20436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.717] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 15
[08-04 13:45:19.717] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.717] [20638 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a1363f0 len 0x4
[08-04 13:45:19.717] [20638 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.717] [20638 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.717] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 15
[08-04 13:45:19.717] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.717] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.717] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.717] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.717] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.717] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 16
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 16
[08-04 13:45:19.719] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.719] [20668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.719] [20668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.736] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 16
[08-04 13:45:19.736] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.736] [20870 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a1380f0 len 0x4
[08-04 13:45:19.736] [20870 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.736] [20870 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.736] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 16
[08-04 13:45:19.736] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.736] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.736] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.736] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.736] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.736] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 17
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 17
[08-04 13:45:19.738] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.738] [20900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.738] [20900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.754] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 17
[08-04 13:45:19.754] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.754] [21102 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a139df0 len 0x4
[08-04 13:45:19.754] [21102 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.754] [21102 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.754] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 17
[08-04 13:45:19.754] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.754] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.754] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.754] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.754] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.754] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 18
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 18
[08-04 13:45:19.757] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.757] [21132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.757] [21132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.773] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 18
[08-04 13:45:19.773] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.773] [21334 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a13baf0 len 0x4
[08-04 13:45:19.773] [21334 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.773] [21334 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.773] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 18
[08-04 13:45:19.773] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.773] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.773] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.773] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.773] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.773] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 19
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 19
[08-04 13:45:19.776] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.776] [21364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.776] [21364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.792] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 19
[08-04 13:45:19.792] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.792] [21566 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a13d7f0 len 0x4
[08-04 13:45:19.792] [21566 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.792] [21566 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.792] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 19
[08-04 13:45:19.792] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.792] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.792] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.792] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.792] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.792] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 20
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x54 len 0x4
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x54 len 0x4
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 20
[08-04 13:45:19.794] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.795] [21596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.795] [21596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.811] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 20
[08-04 13:45:19.811] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.811] [21798 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a13f4f0 len 0x4
[08-04 13:45:19.811] [21798 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.811] [21798 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.811] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 20
[08-04 13:45:19.811] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.811] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.811] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.811] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.811] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.811] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 21
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 21
[08-04 13:45:19.813] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.813] [21828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.813] [21828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.830] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 21
[08-04 13:45:19.830] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.830] [22030 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a1411f0 len 0x4
[08-04 13:45:19.830] [22030 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.830] [22030 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.830] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 21
[08-04 13:45:19.830] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.830] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.830] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.830] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.830] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.830] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 22
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6d len 0x4
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6d len 0x4
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 22
[08-04 13:45:19.832] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.832] [22060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.832] [22060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.848] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 22
[08-04 13:45:19.848] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.848] [22262 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a037330 len 0x4
[08-04 13:45:19.848] [22262 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.848] [22262 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.848] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 22
[08-04 13:45:19.848] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.848] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.848] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.848] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.848] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.848] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.850] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.850] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.850] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.850] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.850] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 23
[08-04 13:45:19.851] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.851] [22290 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 13:45:19.851] [22290 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.851] [22290 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 13:45:19.851] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 23
[08-04 13:45:19.851] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.851] [22292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.851] [22292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.867] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 23
[08-04 13:45:19.867] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.867] [22494 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a039030 len 0x4
[08-04 13:45:19.867] [22494 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.867] [22494 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.867] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 23
[08-04 13:45:19.867] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.867] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.867] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.867] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.867] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.867] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 24
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 24
[08-04 13:45:19.869] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.869] [22524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.869] [22524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.886] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 24
[08-04 13:45:19.886] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.886] [22726 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a03ad30 len 0x4
[08-04 13:45:19.886] [22726 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.886] [22726 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.886] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 24
[08-04 13:45:19.886] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.886] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.886] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.886] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.886] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.886] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 25
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 25
[08-04 13:45:19.888] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.888] [22756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.888] [22756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.905] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 25
[08-04 13:45:19.905] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.905] [22958 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a03ca30 len 0x4
[08-04 13:45:19.905] [22958 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.905] [22958 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.905] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 25
[08-04 13:45:19.905] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.905] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.905] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.905] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.905] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.905] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 26
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x41 len 0x4
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x41 len 0x4
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 26
[08-04 13:45:19.907] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.907] [22988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.907] [22988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.924] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 26
[08-04 13:45:19.924] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.924] [23190 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a03e730 len 0x4
[08-04 13:45:19.924] [23190 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.924] [23190 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.924] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 26
[08-04 13:45:19.924] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.924] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.924] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.924] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.924] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.924] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 27
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 27
[08-04 13:45:19.926] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.927] [23220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.927] [23220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.943] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 27
[08-04 13:45:19.943] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.943] [23422 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a040430 len 0x4
[08-04 13:45:19.943] [23422 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.943] [23422 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.943] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 27
[08-04 13:45:19.943] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.943] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.943] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.943] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.943] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.943] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.945] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.945] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.945] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.945] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.945] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 28
[08-04 13:45:19.945] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.945] [23450 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x67 len 0x4
[08-04 13:45:19.946] [23450 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.946] [23450 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x67 len 0x4
[08-04 13:45:19.946] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 28
[08-04 13:45:19.946] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.946] [23452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.946] [23452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:19.962] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 28
[08-04 13:45:19.962] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.962] [23654 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a042130 len 0x4
[08-04 13:45:19.962] [23654 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.962] [23654 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.962] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 28
[08-04 13:45:19.962] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.962] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.962] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.962] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.962] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.962] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.964] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:19.964] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.965] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.965] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.965] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 29
[08-04 13:45:19.965] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:19.965] [23682 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:45:19.965] [23682 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.965] [23682 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:45:19.965] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 29
[08-04 13:45:19.965] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.965] [23684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.965] [23684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:19.981] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 29
[08-04 13:45:19.981] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:19.981] [23886 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a043e30 len 0x4
[08-04 13:45:19.981] [23886 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.981] [23886 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:19.981] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 29
[08-04 13:45:19.981] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:19.981] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.981] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.981] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.981] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:19.981] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 30
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 30
[08-04 13:45:19.984] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:19.984] [23916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:19.984] [23916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.000] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 30
[08-04 13:45:20.001] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.001] [24118 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a045b30 len 0x4
[08-04 13:45:20.001] [24118 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.001] [24118 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.001] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 30
[08-04 13:45:20.001] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.001] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.001] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.001] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.001] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.001] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 31
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x34 len 0x4
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x34 len 0x4
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 31
[08-04 13:45:20.003] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.003] [24148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.003] [24148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.020] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 31
[08-04 13:45:20.020] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.020] [24350 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a047830 len 0x4
[08-04 13:45:20.020] [24350 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.020] [24350 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.020] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 31
[08-04 13:45:20.020] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.020] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.020] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.020] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.020] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.020] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 32
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 32
[08-04 13:45:20.022] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.022] [24380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.022] [24380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.038] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 32
[08-04 13:45:20.038] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.038] [24582 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a049530 len 0x4
[08-04 13:45:20.038] [24582 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.038] [24582 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.038] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 32
[08-04 13:45:20.038] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.038] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.038] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.038] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.038] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.038] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 33
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 33
[08-04 13:45:20.041] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.041] [24612 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.041] [24612 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.057] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 33
[08-04 13:45:20.057] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.057] [24814 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a04b230 len 0x4
[08-04 13:45:20.057] [24814 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.057] [24814 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.057] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 33
[08-04 13:45:20.057] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.057] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.057] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.057] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.057] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.057] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 34
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 34
[08-04 13:45:20.060] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.060] [24844 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.060] [24844 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.076] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 34
[08-04 13:45:20.076] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.076] [25046 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a04cf30 len 0x4
[08-04 13:45:20.076] [25046 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.076] [25046 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.076] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 34
[08-04 13:45:20.076] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.076] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.076] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.076] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.076] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.076] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 35
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 35
[08-04 13:45:20.078] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.079] [25076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.079] [25076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.095] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 35
[08-04 13:45:20.095] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.095] [25278 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a04ec30 len 0x4
[08-04 13:45:20.095] [25278 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.095] [25278 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.095] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 35
[08-04 13:45:20.095] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.095] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.095] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.095] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.095] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.095] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 36
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 36
[08-04 13:45:20.097] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.097] [25308 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.097] [25308 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.114] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 36
[08-04 13:45:20.114] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.114] [25510 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a050930 len 0x4
[08-04 13:45:20.114] [25510 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.114] [25510 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.114] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 36
[08-04 13:45:20.114] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.114] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.114] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.114] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.114] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.114] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 37
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x2c len 0x4
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x2c len 0x4
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 37
[08-04 13:45:20.116] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.116] [25540 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.116] [25540 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.133] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 37
[08-04 13:45:20.133] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.133] [25742 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a052630 len 0x4
[08-04 13:45:20.133] [25742 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.133] [25742 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.133] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 37
[08-04 13:45:20.133] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.133] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.133] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.133] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.133] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.133] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 38
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 38
[08-04 13:45:20.135] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.135] [25772 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.135] [25772 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.152] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 38
[08-04 13:45:20.152] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.152] [25974 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a054330 len 0x4
[08-04 13:45:20.152] [25974 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.152] [25974 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.152] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 38
[08-04 13:45:20.152] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.152] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.152] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.152] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.152] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.152] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 39
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 39
[08-04 13:45:20.154] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.154] [26004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.154] [26004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.171] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 39
[08-04 13:45:20.171] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.171] [26206 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a056030 len 0x4
[08-04 13:45:20.171] [26206 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.171] [26206 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.171] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 39
[08-04 13:45:20.171] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.171] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.171] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.171] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.171] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.171] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 40
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x33 len 0x4
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x33 len 0x4
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 40
[08-04 13:45:20.173] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.173] [26236 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.173] [26236 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.189] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 40
[08-04 13:45:20.189] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.189] [26438 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a057d30 len 0x4
[08-04 13:45:20.189] [26438 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.189] [26438 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.189] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 40
[08-04 13:45:20.189] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.189] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.189] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.189] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.189] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.189] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 41
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 41
[08-04 13:45:20.192] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.192] [26468 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.192] [26468 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.208] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 41
[08-04 13:45:20.208] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.208] [26670 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a059a30 len 0x4
[08-04 13:45:20.208] [26670 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.208] [26670 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.208] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 41
[08-04 13:45:20.208] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.208] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.208] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.208] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.208] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.208] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.210] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.210] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.210] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.211] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.211] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 42
[08-04 13:45:20.211] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.211] [26698 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-04 13:45:20.211] [26698 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.211] [26698 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-04 13:45:20.211] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 42
[08-04 13:45:20.211] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.211] [26700 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.211] [26700 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.227] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 42
[08-04 13:45:20.227] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.227] [26902 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a05b730 len 0x4
[08-04 13:45:20.227] [26902 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.227] [26902 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.227] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 42
[08-04 13:45:20.227] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.227] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.227] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.227] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.227] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.227] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 43
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 43
[08-04 13:45:20.229] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.230] [26932 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.230] [26932 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.246] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 43
[08-04 13:45:20.246] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.246] [27134 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a05d430 len 0x4
[08-04 13:45:20.246] [27134 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.246] [27134 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.246] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 43
[08-04 13:45:20.246] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.246] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.246] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.246] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.246] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.246] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 44
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 44
[08-04 13:45:20.248] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.248] [27164 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.248] [27164 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.265] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 44
[08-04 13:45:20.265] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.265] [27366 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a05f130 len 0x4
[08-04 13:45:20.265] [27366 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.265] [27366 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.265] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 44
[08-04 13:45:20.265] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.265] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.265] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.265] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.265] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.265] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 45
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x33 len 0x4
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x33 len 0x4
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 45
[08-04 13:45:20.267] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.267] [27396 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.267] [27396 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.283] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 45
[08-04 13:45:20.283] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.283] [27598 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a060e30 len 0x4
[08-04 13:45:20.283] [27598 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.283] [27598 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.283] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 45
[08-04 13:45:20.283] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.283] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.283] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.283] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.283] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.283] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 46
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x37 len 0x4
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x37 len 0x4
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 46
[08-04 13:45:20.286] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.286] [27628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.286] [27628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.313] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 46
[08-04 13:45:20.313] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.313] [27830 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x481722d0 len 0x4
[08-04 13:45:20.313] [27830 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.313] [27830 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.313] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 46
[08-04 13:45:20.313] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.313] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.313] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.313] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.313] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.313] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 47
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 47
[08-04 13:45:20.316] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.317] [27860 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.317] [27860 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.332] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 47
[08-04 13:45:20.332] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.332] [28036 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a3c6640 len 0x4
[08-04 13:45:20.332] [28036 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.332] [28036 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.332] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 47
[08-04 13:45:20.332] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.332] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.332] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.332] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.332] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.332] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 48
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 48
[08-04 13:45:20.334] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.335] [28066 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.335] [28066 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.341] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 48
[08-04 13:45:20.341] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.341] [28142 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a15b960 len 0x4
[08-04 13:45:20.341] [28142 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.341] [28142 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.341] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 48
[08-04 13:45:20.341] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.341] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.341] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.341] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.341] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.341] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 49
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 49
[08-04 13:45:20.343] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.344] [28172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.344] [28172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.785] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 49
[08-04 13:45:20.785] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.785] [33506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a023f90 len 0x4
[08-04 13:45:20.785] [33506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.785] [33506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.785] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 49
[08-04 13:45:20.785] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.785] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.785] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.785] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.785] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.785] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 50
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 50
[08-04 13:45:20.787] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.787] [33536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.787] [33536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.804] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 50
[08-04 13:45:20.804] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.804] [33738 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a062990 len 0x4
[08-04 13:45:20.804] [33738 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.804] [33738 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.804] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 50
[08-04 13:45:20.804] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.804] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.804] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.804] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.804] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.804] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 51
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 51
[08-04 13:45:20.806] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.806] [33768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.806] [33768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.823] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 51
[08-04 13:45:20.823] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.823] [33970 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a064690 len 0x4
[08-04 13:45:20.823] [33970 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.823] [33970 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.823] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 51
[08-04 13:45:20.823] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.823] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.823] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.823] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.823] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.823] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 52
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x77 len 0x4
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x77 len 0x4
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 52
[08-04 13:45:20.825] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.825] [34 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.825] [34 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.841] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 52
[08-04 13:45:20.841] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.841] [34202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a066390 len 0x4
[08-04 13:45:20.841] [34202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.841] [34202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.841] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 52
[08-04 13:45:20.841] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.841] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.841] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.841] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.841] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.841] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 53
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 53
[08-04 13:45:20.844] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.844] [34232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.844] [34232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.860] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 53
[08-04 13:45:20.860] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.860] [34434 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a068090 len 0x4
[08-04 13:45:20.860] [34434 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.860] [34434 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.860] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 53
[08-04 13:45:20.860] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.860] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.860] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.860] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.860] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.860] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 54
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 54
[08-04 13:45:20.862] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.863] [34464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.863] [34464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.879] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 54
[08-04 13:45:20.879] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.879] [34666 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a069d90 len 0x4
[08-04 13:45:20.879] [34666 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.879] [34666 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.879] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 54
[08-04 13:45:20.879] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.879] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.879] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.879] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.879] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.879] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 55
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 55
[08-04 13:45:20.881] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.881] [34696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.881] [34696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.898] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 55
[08-04 13:45:20.898] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.898] [34898 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a06ba90 len 0x4
[08-04 13:45:20.898] [34898 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.898] [34898 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.898] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 55
[08-04 13:45:20.898] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.898] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.898] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.898] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.898] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.898] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 56
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61 len 0x4
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x61 len 0x4
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 56
[08-04 13:45:20.900] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.901] [34928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.901] [34928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.917] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 56
[08-04 13:45:20.917] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.917] [35130 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49fe2aa0 len 0x4
[08-04 13:45:20.917] [35130 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.917] [35130 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.917] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 56
[08-04 13:45:20.917] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.917] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.917] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.917] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.917] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.917] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 57
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 57
[08-04 13:45:20.920] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.920] [35160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.920] [35160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.936] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 57
[08-04 13:45:20.936] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.936] [35362 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49fe47a0 len 0x4
[08-04 13:45:20.936] [35362 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.937] [35362 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.937] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 57
[08-04 13:45:20.937] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.937] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.937] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.937] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.937] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.937] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 58
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 58
[08-04 13:45:20.939] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.939] [35392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.939] [35392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.956] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 58
[08-04 13:45:20.956] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.956] [35594 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49fe64a0 len 0x4
[08-04 13:45:20.956] [35594 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.956] [35594 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.956] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 58
[08-04 13:45:20.956] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.956] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.956] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.956] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.956] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.956] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 59
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 59
[08-04 13:45:20.958] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.958] [35624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.958] [35624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:20.975] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 59
[08-04 13:45:20.975] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.975] [35826 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49fe81a0 len 0x4
[08-04 13:45:20.975] [35826 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.975] [35826 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.975] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 59
[08-04 13:45:20.975] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.975] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.975] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.975] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.975] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.975] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 60
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 60
[08-04 13:45:20.977] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.977] [35856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.977] [35856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:20.994] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 60
[08-04 13:45:20.994] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:20.994] [36058 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49fe9e80 len 0x4
[08-04 13:45:20.994] [36058 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.994] [36058 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:20.994] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 60
[08-04 13:45:20.994] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:20.994] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.994] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.994] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.994] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:20.994] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 61
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 61
[08-04 13:45:20.996] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:20.997] [36088 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:20.997] [36088 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:21.013] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 61
[08-04 13:45:21.013] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:21.013] [36290 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49febb80 len 0x4
[08-04 13:45:21.013] [36290 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.013] [36290 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:21.013] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 61
[08-04 13:45:21.013] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:21.013] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.013] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.013] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.013] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.013] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 62
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 62
[08-04 13:45:21.016] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:21.016] [36320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:21.016] [36320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:21.032] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 62
[08-04 13:45:21.032] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:21.032] [36522 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49fed880 len 0x4
[08-04 13:45:21.032] [36522 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.032] [36522 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:21.032] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 62
[08-04 13:45:21.032] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:21.032] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.032] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.032] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.032] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.032] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 63
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 63
[08-04 13:45:21.035] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:21.035] [36552 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:21.035] [36552 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:21.051] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 63
[08-04 13:45:21.051] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:21.051] [36754 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49fefeb0 len 0x4
[08-04 13:45:21.051] [36754 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.051] [36754 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:21.051] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 63
[08-04 13:45:21.051] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:21.051] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.051] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.051] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.051] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.051] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 64
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 64
[08-04 13:45:21.054] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:21.054] [36784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:21.054] [36784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:21.070] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 64
[08-04 13:45:21.070] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:21.070] [36986 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49ff1bb0 len 0x4
[08-04 13:45:21.070] [36986 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.070] [36986 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:21.070] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 64
[08-04 13:45:21.070] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:21.070] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.070] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.070] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.070] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.070] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 65
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49 len 0x4
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x49 len 0x4
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 65
[08-04 13:45:21.072] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:21.073] [37016 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:21.073] [37016 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:21.089] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 65
[08-04 13:45:21.089] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:21.089] [37218 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49ff38b0 len 0x4
[08-04 13:45:21.089] [37218 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.089] [37218 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:21.089] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 65
[08-04 13:45:21.089] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:21.089] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.089] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.089] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.089] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.089] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 66
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4c len 0x4
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4c len 0x4
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 66
[08-04 13:45:21.091] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:21.091] [37248 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:21.091] [37248 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:21.108] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 66
[08-04 13:45:21.108] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:21.108] [37450 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49ff55b0 len 0x4
[08-04 13:45:21.108] [37450 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.108] [37450 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:21.108] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 66
[08-04 13:45:21.108] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:21.108] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.108] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.108] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.108] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.108] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 67
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 67
[08-04 13:45:21.110] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:21.110] [37480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:21.110] [37480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
[08-04 13:45:21.127] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad4a0c4ea0, reqid: 67
[08-04 13:45:21.127] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:21.127] [37682 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49ff72b0 len 0x4
[08-04 13:45:21.127] [37682 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.127] [37682 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:21.127] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 67
[08-04 13:45:21.127] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:21.127] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.127] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.127] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.127] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.127] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 68
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 68
[08-04 13:45:21.129] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:21.129] [37712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:21.129] [37712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:21.144] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 68
[08-04 13:45:21.144] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:21.144] [37888 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49ff8c70 len 0x4
[08-04 13:45:21.144] [37888 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.144] [37888 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:21.144] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 68
[08-04 13:45:21.144] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:21.144] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.144] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.144] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.144] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.144] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad481757b0, reqid: 69
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad4a0c4ea0
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad481757b0, wt: 0x7fad4a0c4ea0
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 69
[08-04 13:45:21.146] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:21.146] [37918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:21.146] [37918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad481757b0
[08-04 13:45:21.152] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fad481757b0, reqid: 69
[08-04 13:45:21.152] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 13:45:21.152] [37994 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49ff99b0 len 0x4
[08-04 13:45:21.152] [37994 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.152] [37994 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 13:45:21.152] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 69
[08-04 13:45:21.152] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 13:45:21.152] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.152] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.152] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.152] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 13:45:21.152] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fad481757b0
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fad4a0c4ea0, reqid: 70
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fad4a0c4ea0, wt: 0x7fad481757b0
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 70
[08-04 13:45:21.154] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 13:45:21.155] [38024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 13:45:21.155] [38024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fad4a0c4ea0
