[08-04 09:55:22.696] [0 s] axi-master n900_vnice_npu_soc.n900_vnice.mem__128_axi_m binding device n900_vnice_npu_soc.bus.s_0
[08-04 09:55:22.699] [0 s] nice-master n900_vnice_npu_soc.n900_vnice.nice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.nice
[08-04 09:55:22.702] [0 s] vnice-master n900_vnice_npu_soc.n900_vnice.vnice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.vnice
[08-04 09:55:22.707] [0 s] axi-slave stub n900_vnice_npu_soc.n900_vnice.Vn900_core_rams port n900_vnice_npu_soc.n900_vnice.slv__64_axi_s
[08-04 09:55:22.709] [0 s] ------- load n900_vnice_npu_soc.bus route rules: -------
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_0: slave0 to master0 n900_vnice_npu_soc.acc0 addr_range: start 0x100000 -- end 0x1fffff, length 0x100000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_1: slave0 to master1 n900_vnice_npu_soc.mrom addr_range: start 0x0 -- end 0xfffff, length 0x100000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_2: slave0 to master2 n900_vnice_npu_soc.gpio addr_range: start 0x10012000 -- end 0x10012fff, length 0x1000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_3: slave0 to master3 n900_vnice_npu_soc.uart addr_range: start 0x10013000 -- end 0x10013fff, length 0x1000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_4: slave0 to master4 n900_vnice_npu_soc.qspi0 addr_range: start 0x10014000 -- end 0x10023fff, length 0x10000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_5: slave0 to master5 n900_vnice_npu_soc.qspi1 addr_range: start 0x60000000 -- end 0x6effffff, length 0xf000000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_6: slave0 to master6 n900_vnice_npu_soc.hole addr_range: start 0x50000000 -- end 0x5fedffff, length 0xfee0000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_7: slave0 to master7 n900_vnice_npu_soc.xip addr_range: start 0x20000000 -- end 0x3fffffff, length 0x20000000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_8: slave0 to master8 n900_vnice_npu_soc.ddr addr_range: start 0x70180000 -- end 0x9017ffff, length 0x20000000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_9: slave0 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_10: slave0 to master10 n900_vnice_npu_soc.n900_vnice addr_range: start 0x70000000 -- end 0x7017ffff, length 0x180000
[08-04 09:55:22.709] [0 s] n900_vnice_npu_soc.bus rule_11: slave1 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[08-04 09:55:23.873] [0 s] acc0 ------ load_images -------
[08-04 09:55:23.873] [0 s] acc1 ------ load_images -------
[08-04 09:55:23.873] [0 s] bus ------ load_images -------
[08-04 09:55:23.873] [0 s] ddr ------ load_images -------
[08-04 09:55:23.873] [0 s] gpio ------ load_images -------
[08-04 09:55:23.873] [0 s] hole ------ load_images -------
[08-04 09:55:23.873] [0 s] mrom ------ load_images -------
[08-04 09:55:23.873] [0 s] n900_vnice ------ load_images -------
[08-04 09:55:23.873] [0 s] nice_remote_adapter ------ load_images -------
[08-04 09:55:23.873] [0 s] qspi0 ------ load_images -------
[08-04 09:55:23.873] [0 s] qspi1 ------ load_images -------
[08-04 09:55:23.873] [0 s] xip ------ load_images -------
[08-04 09:55:23.876] [40 ns] mem__128_axi_m_0_bridge ------ load_images -------
[08-04 09:55:23.877] [40 ns] load_images: ./fw/test_noc.elf offset 0x0
[08-04 09:55:23.877] [40 ns] load_elf: file ./fw/test_noc.elf with 3 segments
[08-04 09:55:23.877] [40 ns] load_elf: segment 0x70000000         .. 0x70001f97        
[08-04 09:55:23.877] [40 ns] load_elf: segment 0x70100000         .. 0x70100dff        
[08-04 09:55:23.877] [40 ns] load_elf: segment 0x70100e00         .. 0x701015ff        
[08-04 09:55:23.877] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.124] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb064403c0
[08-04 09:55:24.125] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.125] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.125] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.125] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013018, tr: 0x7ffb060c4ea0, reqid: 0
[08-04 09:55:24.125] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013018, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb064403c0
[08-04 09:55:24.125] [6022 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x18 data 0x0 len 0x4
[08-04 09:55:24.125] [6022 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x18 len 0x4  in pv mode
[08-04 09:55:24.125] [6022 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013018 data 0x0 len 0x4
[08-04 09:55:24.125] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 0
[08-04 09:55:24.125] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.125] [6024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.125] [6024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.126] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013008, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 0
[08-04 09:55:24.126] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.126] [6048 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x8 data 0x60005e0 len 0x4
[08-04 09:55:24.126] [6048 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x8 len 0x4  in pv mode
[08-04 09:55:24.126] [6048 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013008 data 0x0 len 0x4
[08-04 09:55:24.126] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 0
[08-04 09:55:24.126] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.126] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.126] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.126] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.126] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.126] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013008, tr: 0x7ffb060c4ea0, reqid: 1
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013008, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x8 data 0x1 len 0x4
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x8 len 0x4  in pv mode
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013008 data 0x1 len 0x4
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 1
[08-04 09:55:24.127] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.127] [6078 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.127] [6078 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.129] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x1001300c, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 1
[08-04 09:55:24.129] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.129] [6104 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0xc data 0x6000d70 len 0x4
[08-04 09:55:24.129] [6104 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0xc len 0x4  in pv mode
[08-04 09:55:24.129] [6104 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x1001300c data 0x0 len 0x4
[08-04 09:55:24.129] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 1
[08-04 09:55:24.129] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.129] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.129] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.129] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.129] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.129] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 1001300c, tr: 0x7ffb060c4ea0, reqid: 2
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x1001300c, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0xc data 0x1 len 0x4
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0xc len 0x4  in pv mode
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x1001300c data 0x1 len 0x4
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 2
[08-04 09:55:24.130] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.130] [6134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.130] [6134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.632] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 2
[08-04 09:55:24.632] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.632] [17622 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fec160 len 0x4
[08-04 09:55:24.632] [17622 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.632] [17622 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.632] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 2
[08-04 09:55:24.632] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.632] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.632] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.632] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.632] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.632] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 3
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4e len 0x4
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4e len 0x4
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 3
[08-04 09:55:24.634] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.634] [17652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.634] [17652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.643] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 3
[08-04 09:55:24.643] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.643] [17854 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fede60 len 0x4
[08-04 09:55:24.643] [17854 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.643] [17854 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.643] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 3
[08-04 09:55:24.643] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.643] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.643] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.643] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.643] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.643] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 4
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 4
[08-04 09:55:24.644] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.644] [17884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.644] [17884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.654] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 4
[08-04 09:55:24.654] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.654] [18086 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61224f0 len 0x4
[08-04 09:55:24.654] [18086 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.654] [18086 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.654] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 4
[08-04 09:55:24.654] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.654] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.654] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.654] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.654] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.654] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 5
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63 len 0x4
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x63 len 0x4
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 5
[08-04 09:55:24.655] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.655] [18116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.655] [18116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.664] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 5
[08-04 09:55:24.664] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.664] [18318 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61241f0 len 0x4
[08-04 09:55:24.664] [18318 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.664] [18318 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.664] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 5
[08-04 09:55:24.664] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.664] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.664] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.664] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.664] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.664] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 6
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 6
[08-04 09:55:24.665] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.665] [18348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.665] [18348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.674] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 6
[08-04 09:55:24.674] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.674] [18550 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6125ef0 len 0x4
[08-04 09:55:24.674] [18550 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.674] [18550 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.674] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 6
[08-04 09:55:24.674] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.674] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.674] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.674] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.674] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.674] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 7
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 7
[08-04 09:55:24.675] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.676] [18580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.676] [18580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.684] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 7
[08-04 09:55:24.684] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.684] [18782 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6127bf0 len 0x4
[08-04 09:55:24.684] [18782 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.684] [18782 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.684] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 7
[08-04 09:55:24.684] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.684] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.684] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.684] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.684] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.684] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 8
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 8
[08-04 09:55:24.686] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.686] [18812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.686] [18812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.695] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 8
[08-04 09:55:24.695] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.695] [19014 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61298f0 len 0x4
[08-04 09:55:24.695] [19014 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.695] [19014 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.695] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 8
[08-04 09:55:24.695] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.695] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.695] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.695] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.695] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.695] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 9
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 9
[08-04 09:55:24.696] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.696] [19044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.696] [19044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.705] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 9
[08-04 09:55:24.705] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.705] [19246 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x612b5f0 len 0x4
[08-04 09:55:24.705] [19246 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.705] [19246 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.705] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 9
[08-04 09:55:24.705] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.705] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.705] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.705] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.705] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.705] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 10
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x53 len 0x4
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x53 len 0x4
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 10
[08-04 09:55:24.706] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.706] [19276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.706] [19276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.715] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 10
[08-04 09:55:24.715] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.715] [19478 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x612d2f0 len 0x4
[08-04 09:55:24.715] [19478 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.715] [19478 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.715] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 10
[08-04 09:55:24.715] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.715] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.715] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.715] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.715] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.715] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 11
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 11
[08-04 09:55:24.717] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.717] [19508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.717] [19508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.725] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 11
[08-04 09:55:24.725] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.725] [19710 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x612eff0 len 0x4
[08-04 09:55:24.725] [19710 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.725] [19710 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.725] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 11
[08-04 09:55:24.725] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.725] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.725] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.725] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.725] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.725] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 12
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4b len 0x4
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4b len 0x4
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 12
[08-04 09:55:24.727] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.727] [19740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.727] [19740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.736] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 12
[08-04 09:55:24.736] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.736] [19942 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6130cf0 len 0x4
[08-04 09:55:24.736] [19942 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.736] [19942 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.736] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 12
[08-04 09:55:24.736] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.736] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.736] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.736] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.736] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.736] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 13
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 13
[08-04 09:55:24.737] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.737] [19972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.737] [19972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.746] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 13
[08-04 09:55:24.746] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.746] [20174 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61329f0 len 0x4
[08-04 09:55:24.746] [20174 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.746] [20174 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.746] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 13
[08-04 09:55:24.746] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.746] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.746] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.746] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.746] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.746] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 14
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x42 len 0x4
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x42 len 0x4
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 14
[08-04 09:55:24.747] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.747] [20204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.747] [20204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.756] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 14
[08-04 09:55:24.756] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.756] [20406 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61346f0 len 0x4
[08-04 09:55:24.756] [20406 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.756] [20406 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.756] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 14
[08-04 09:55:24.756] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.756] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.756] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.756] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.756] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.756] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 15
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 15
[08-04 09:55:24.757] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.757] [20436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.757] [20436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.766] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 15
[08-04 09:55:24.766] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.766] [20638 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61363f0 len 0x4
[08-04 09:55:24.766] [20638 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.766] [20638 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.766] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 15
[08-04 09:55:24.766] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.766] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.766] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.766] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.766] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.766] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 16
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 16
[08-04 09:55:24.768] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.768] [20668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.768] [20668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.776] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 16
[08-04 09:55:24.776] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.776] [20870 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61380f0 len 0x4
[08-04 09:55:24.776] [20870 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.776] [20870 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.776] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 16
[08-04 09:55:24.776] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.776] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.776] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.776] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.776] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.776] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 17
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 17
[08-04 09:55:24.778] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.778] [20900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.778] [20900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.786] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 17
[08-04 09:55:24.786] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.786] [21102 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6139df0 len 0x4
[08-04 09:55:24.786] [21102 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.786] [21102 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.786] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 17
[08-04 09:55:24.786] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.786] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.786] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.786] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.786] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.786] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 18
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 18
[08-04 09:55:24.788] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.788] [21132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.788] [21132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.797] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 18
[08-04 09:55:24.797] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.797] [21334 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x613baf0 len 0x4
[08-04 09:55:24.797] [21334 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.797] [21334 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.797] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 18
[08-04 09:55:24.797] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.797] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.797] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.797] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.797] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.797] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 19
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 19
[08-04 09:55:24.798] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.798] [21364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.798] [21364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.807] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 19
[08-04 09:55:24.807] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.807] [21566 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x613d7f0 len 0x4
[08-04 09:55:24.807] [21566 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.807] [21566 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.807] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 19
[08-04 09:55:24.807] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.807] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.807] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.807] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.807] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.807] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 20
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x54 len 0x4
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x54 len 0x4
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 20
[08-04 09:55:24.808] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.808] [21596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.808] [21596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.817] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 20
[08-04 09:55:24.817] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.817] [21798 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x613f4f0 len 0x4
[08-04 09:55:24.817] [21798 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.817] [21798 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.817] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 20
[08-04 09:55:24.817] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.817] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.817] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.817] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.817] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.817] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 21
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 21
[08-04 09:55:24.818] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.818] [21828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.818] [21828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.827] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 21
[08-04 09:55:24.827] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.827] [22030 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61411f0 len 0x4
[08-04 09:55:24.827] [22030 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.827] [22030 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.827] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 21
[08-04 09:55:24.827] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.827] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.827] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.827] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.827] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.827] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 22
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6d len 0x4
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6d len 0x4
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 22
[08-04 09:55:24.828] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.828] [22060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.828] [22060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.837] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 22
[08-04 09:55:24.837] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.837] [22262 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6037330 len 0x4
[08-04 09:55:24.837] [22262 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.837] [22262 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.837] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 22
[08-04 09:55:24.837] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.837] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.837] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.837] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.837] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.837] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 23
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 23
[08-04 09:55:24.838] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.838] [22292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.838] [22292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.847] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 23
[08-04 09:55:24.847] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.847] [22494 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6039030 len 0x4
[08-04 09:55:24.847] [22494 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.847] [22494 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.847] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 23
[08-04 09:55:24.847] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.847] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.847] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.847] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.847] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.847] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 24
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 24
[08-04 09:55:24.848] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.848] [22524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.848] [22524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.857] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 24
[08-04 09:55:24.857] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.857] [22726 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x603ad30 len 0x4
[08-04 09:55:24.857] [22726 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.857] [22726 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.857] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 24
[08-04 09:55:24.857] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.857] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.857] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.857] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.857] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.857] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 25
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 25
[08-04 09:55:24.858] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.858] [22756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.858] [22756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.867] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 25
[08-04 09:55:24.867] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.867] [22958 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x603ca30 len 0x4
[08-04 09:55:24.867] [22958 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.867] [22958 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.867] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 25
[08-04 09:55:24.867] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.867] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.867] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.867] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.867] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.867] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 26
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x41 len 0x4
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x41 len 0x4
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 26
[08-04 09:55:24.868] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.868] [22988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.868] [22988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.877] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 26
[08-04 09:55:24.877] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.877] [23190 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x603e730 len 0x4
[08-04 09:55:24.877] [23190 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.877] [23190 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.877] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 26
[08-04 09:55:24.877] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.877] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.877] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.877] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.877] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.877] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 27
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 27
[08-04 09:55:24.878] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.878] [23220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.878] [23220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.887] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 27
[08-04 09:55:24.887] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.887] [23422 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6040430 len 0x4
[08-04 09:55:24.887] [23422 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.887] [23422 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.887] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 27
[08-04 09:55:24.887] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.887] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.887] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.887] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.887] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.887] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 28
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x67 len 0x4
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x67 len 0x4
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 28
[08-04 09:55:24.888] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.888] [23452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.888] [23452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.897] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 28
[08-04 09:55:24.897] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.897] [23654 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6042130 len 0x4
[08-04 09:55:24.897] [23654 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.897] [23654 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.897] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 28
[08-04 09:55:24.897] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.897] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.897] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.897] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.897] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.897] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 29
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 29
[08-04 09:55:24.898] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.898] [23684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.898] [23684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.907] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 29
[08-04 09:55:24.907] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.907] [23886 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6043e30 len 0x4
[08-04 09:55:24.907] [23886 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.907] [23886 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.907] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 29
[08-04 09:55:24.907] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.907] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.907] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.907] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.907] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.907] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 30
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 30
[08-04 09:55:24.909] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.909] [23916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.909] [23916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.918] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 30
[08-04 09:55:24.918] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.918] [24118 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6045b30 len 0x4
[08-04 09:55:24.918] [24118 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.918] [24118 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.918] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 30
[08-04 09:55:24.918] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.918] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.918] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.918] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.918] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.918] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 31
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x34 len 0x4
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x34 len 0x4
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 31
[08-04 09:55:24.919] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.919] [24148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.919] [24148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.928] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 31
[08-04 09:55:24.928] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.928] [24350 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6047830 len 0x4
[08-04 09:55:24.928] [24350 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.928] [24350 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.928] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 31
[08-04 09:55:24.928] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.928] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.928] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.928] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.928] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.928] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.929] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.929] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.929] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.929] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.929] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 32
[08-04 09:55:24.929] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.929] [24378 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:24.929] [24378 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.930] [24378 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:24.930] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 32
[08-04 09:55:24.930] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.930] [24380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.930] [24380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.938] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 32
[08-04 09:55:24.938] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.938] [24582 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6049530 len 0x4
[08-04 09:55:24.938] [24582 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.938] [24582 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.938] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 32
[08-04 09:55:24.938] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.938] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.938] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.938] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.938] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.938] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 33
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 33
[08-04 09:55:24.940] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.940] [24612 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.940] [24612 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.949] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 33
[08-04 09:55:24.949] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.949] [24814 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x604b230 len 0x4
[08-04 09:55:24.949] [24814 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.949] [24814 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.949] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 33
[08-04 09:55:24.949] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.949] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.949] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.949] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.949] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.949] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 34
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 34
[08-04 09:55:24.950] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.950] [24844 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.950] [24844 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.959] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 34
[08-04 09:55:24.959] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.959] [25046 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x604cf30 len 0x4
[08-04 09:55:24.959] [25046 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.959] [25046 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.959] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 34
[08-04 09:55:24.959] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.959] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.959] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.959] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.959] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.959] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 35
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 35
[08-04 09:55:24.960] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.960] [25076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.960] [25076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.969] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 35
[08-04 09:55:24.969] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.969] [25278 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x604ec30 len 0x4
[08-04 09:55:24.969] [25278 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.969] [25278 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.969] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 35
[08-04 09:55:24.969] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.969] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.969] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.969] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.969] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.969] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 36
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 36
[08-04 09:55:24.970] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.970] [25308 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.970] [25308 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:24.980] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 36
[08-04 09:55:24.980] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.980] [25510 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6050930 len 0x4
[08-04 09:55:24.980] [25510 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.980] [25510 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.980] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 36
[08-04 09:55:24.980] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.980] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.980] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.980] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.980] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.980] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 37
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x2c len 0x4
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x2c len 0x4
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 37
[08-04 09:55:24.981] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.981] [25540 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.981] [25540 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:24.990] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 37
[08-04 09:55:24.990] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:24.990] [25742 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6052630 len 0x4
[08-04 09:55:24.990] [25742 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.990] [25742 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:24.990] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 37
[08-04 09:55:24.990] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:24.990] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.990] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.990] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.990] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:24.990] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 38
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 38
[08-04 09:55:24.991] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:24.991] [25772 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:24.991] [25772 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.000] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 38
[08-04 09:55:25.000] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.000] [25974 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6054330 len 0x4
[08-04 09:55:25.000] [25974 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.000] [25974 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.000] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 38
[08-04 09:55:25.000] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.000] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.000] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.000] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.000] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.000] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 39
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 39
[08-04 09:55:25.001] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.002] [26004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.002] [26004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.010] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 39
[08-04 09:55:25.010] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.010] [26206 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6056030 len 0x4
[08-04 09:55:25.010] [26206 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.010] [26206 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.010] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 39
[08-04 09:55:25.010] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.010] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.010] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.010] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.010] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.010] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 40
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x39 len 0x4
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x39 len 0x4
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 40
[08-04 09:55:25.012] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.012] [26236 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.012] [26236 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.021] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 40
[08-04 09:55:25.021] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.021] [26438 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6057d30 len 0x4
[08-04 09:55:25.021] [26438 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.021] [26438 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.021] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 40
[08-04 09:55:25.021] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.021] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.021] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.021] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.021] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.021] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 41
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 41
[08-04 09:55:25.022] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.022] [26468 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.022] [26468 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.031] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 41
[08-04 09:55:25.031] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.031] [26670 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6059a30 len 0x4
[08-04 09:55:25.031] [26670 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.031] [26670 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.031] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 41
[08-04 09:55:25.031] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.031] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.031] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.031] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.031] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.031] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 42
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x34 len 0x4
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x34 len 0x4
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 42
[08-04 09:55:25.032] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.032] [26700 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.032] [26700 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.041] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 42
[08-04 09:55:25.041] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.041] [26902 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x605b730 len 0x4
[08-04 09:55:25.041] [26902 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.041] [26902 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.041] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 42
[08-04 09:55:25.041] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.041] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.041] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.041] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.041] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.041] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 43
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 43
[08-04 09:55:25.042] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.043] [26932 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.043] [26932 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.052] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 43
[08-04 09:55:25.052] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.052] [27134 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x605d430 len 0x4
[08-04 09:55:25.052] [27134 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.052] [27134 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.052] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 43
[08-04 09:55:25.052] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.052] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.052] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.052] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.052] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.052] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 44
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 44
[08-04 09:55:25.053] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.053] [27164 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.053] [27164 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.062] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 44
[08-04 09:55:25.062] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.062] [27366 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x605f130 len 0x4
[08-04 09:55:25.062] [27366 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.062] [27366 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.062] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 44
[08-04 09:55:25.062] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.062] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.062] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.062] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.062] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.062] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 45
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 45
[08-04 09:55:25.063] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.063] [27396 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.063] [27396 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.072] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 45
[08-04 09:55:25.072] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.072] [27598 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6060e30 len 0x4
[08-04 09:55:25.072] [27598 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.072] [27598 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.072] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 45
[08-04 09:55:25.072] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.072] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.072] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.072] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.072] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.072] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 46
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 46
[08-04 09:55:25.074] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.074] [27628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.074] [27628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.093] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 46
[08-04 09:55:25.093] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.093] [27830 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x41723d0 len 0x4
[08-04 09:55:25.093] [27830 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.093] [27830 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.093] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 46
[08-04 09:55:25.093] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.093] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.093] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.093] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.093] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.093] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 47
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 47
[08-04 09:55:25.095] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.095] [27860 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.095] [27860 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.104] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 47
[08-04 09:55:25.104] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.104] [28036 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63c6640 len 0x4
[08-04 09:55:25.104] [28036 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.104] [28036 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.104] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 47
[08-04 09:55:25.104] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.104] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.104] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.104] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.104] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.104] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 48
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 48
[08-04 09:55:25.105] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.105] [28066 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.105] [28066 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.109] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 48
[08-04 09:55:25.109] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.109] [28142 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x615b960 len 0x4
[08-04 09:55:25.109] [28142 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.109] [28142 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.109] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 48
[08-04 09:55:25.109] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.109] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.109] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.109] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.109] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.109] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 49
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 49
[08-04 09:55:25.110] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.110] [28172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.110] [28172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.348] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 49
[08-04 09:55:25.348] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.348] [33506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6023f90 len 0x4
[08-04 09:55:25.348] [33506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.348] [33506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.348] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 49
[08-04 09:55:25.348] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.348] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.348] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.348] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.348] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.348] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 50
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 50
[08-04 09:55:25.349] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.349] [33536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.349] [33536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.358] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 50
[08-04 09:55:25.358] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.358] [33738 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6062990 len 0x4
[08-04 09:55:25.358] [33738 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.358] [33738 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.358] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 50
[08-04 09:55:25.358] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.358] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.358] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.358] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.358] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.358] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 51
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 51
[08-04 09:55:25.360] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.360] [33768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.360] [33768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.369] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 51
[08-04 09:55:25.369] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.369] [33970 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6064690 len 0x4
[08-04 09:55:25.369] [33970 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.369] [33970 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.369] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 51
[08-04 09:55:25.369] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.369] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.369] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.369] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.369] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.369] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 52
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x77 len 0x4
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x77 len 0x4
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 52
[08-04 09:55:25.370] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.370] [34 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.370] [34 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.379] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 52
[08-04 09:55:25.379] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.379] [34202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6066390 len 0x4
[08-04 09:55:25.379] [34202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.379] [34202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.379] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 52
[08-04 09:55:25.379] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.379] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.379] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.379] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.379] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.379] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 53
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 53
[08-04 09:55:25.380] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.381] [34232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.381] [34232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.389] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 53
[08-04 09:55:25.389] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.389] [34434 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6068090 len 0x4
[08-04 09:55:25.389] [34434 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.389] [34434 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.389] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 53
[08-04 09:55:25.389] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.389] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.389] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.389] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.389] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.389] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 54
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 54
[08-04 09:55:25.391] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.391] [34464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.391] [34464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.400] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 54
[08-04 09:55:25.400] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.400] [34666 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6069d90 len 0x4
[08-04 09:55:25.400] [34666 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.400] [34666 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.400] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 54
[08-04 09:55:25.400] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.400] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.400] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.400] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.400] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.400] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 55
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 55
[08-04 09:55:25.401] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.401] [34696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.401] [34696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.410] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 55
[08-04 09:55:25.410] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.410] [34898 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x606ba90 len 0x4
[08-04 09:55:25.410] [34898 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.410] [34898 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.410] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 55
[08-04 09:55:25.410] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.410] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.410] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.410] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.410] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.410] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 56
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61 len 0x4
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x61 len 0x4
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 56
[08-04 09:55:25.411] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.411] [34928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.411] [34928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.420] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 56
[08-04 09:55:25.420] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.420] [35130 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fe2aa0 len 0x4
[08-04 09:55:25.420] [35130 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.420] [35130 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.420] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 56
[08-04 09:55:25.420] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.420] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.420] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.420] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.420] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.420] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 57
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 57
[08-04 09:55:25.421] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.422] [35160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.422] [35160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.430] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 57
[08-04 09:55:25.430] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.430] [35362 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fe47a0 len 0x4
[08-04 09:55:25.430] [35362 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.430] [35362 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.430] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 57
[08-04 09:55:25.430] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.430] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.430] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.430] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.430] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.430] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 58
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 58
[08-04 09:55:25.432] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.432] [35392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.432] [35392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.440] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 58
[08-04 09:55:25.440] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.440] [35594 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fe64a0 len 0x4
[08-04 09:55:25.440] [35594 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.440] [35594 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.440] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 58
[08-04 09:55:25.440] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.440] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.440] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.440] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.440] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.440] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 59
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 59
[08-04 09:55:25.442] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.442] [35624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.442] [35624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.450] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 59
[08-04 09:55:25.450] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.450] [35826 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fe81a0 len 0x4
[08-04 09:55:25.450] [35826 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.450] [35826 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.450] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 59
[08-04 09:55:25.450] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.450] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.450] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.450] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.450] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.450] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 60
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 60
[08-04 09:55:25.452] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.452] [35856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.452] [35856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.461] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 60
[08-04 09:55:25.461] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.461] [36058 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fe9e80 len 0x4
[08-04 09:55:25.461] [36058 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.461] [36058 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.461] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 60
[08-04 09:55:25.461] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.461] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.461] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.461] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.461] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.461] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 61
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 61
[08-04 09:55:25.462] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.462] [36088 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.462] [36088 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.471] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 61
[08-04 09:55:25.471] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.471] [36290 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5febb80 len 0x4
[08-04 09:55:25.471] [36290 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.471] [36290 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.471] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 61
[08-04 09:55:25.471] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.471] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.471] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.471] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.471] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.471] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 62
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 62
[08-04 09:55:25.472] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.472] [36320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.472] [36320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.481] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 62
[08-04 09:55:25.481] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.481] [36522 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fed880 len 0x4
[08-04 09:55:25.481] [36522 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.481] [36522 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.481] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 62
[08-04 09:55:25.481] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.481] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.481] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.481] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.481] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.481] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.482] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.482] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.482] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.482] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.482] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 63
[08-04 09:55:25.482] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.482] [36550 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 09:55:25.482] [36550 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.482] [36550 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 09:55:25.482] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 63
[08-04 09:55:25.483] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.483] [36552 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.483] [36552 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.491] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 63
[08-04 09:55:25.491] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.491] [36754 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fefeb0 len 0x4
[08-04 09:55:25.491] [36754 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.491] [36754 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.491] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 63
[08-04 09:55:25.491] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.491] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.491] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.491] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.491] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.491] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 64
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 64
[08-04 09:55:25.493] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.493] [36784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.493] [36784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.501] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 64
[08-04 09:55:25.501] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.501] [36986 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff1bb0 len 0x4
[08-04 09:55:25.501] [36986 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.501] [36986 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.501] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 64
[08-04 09:55:25.501] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.501] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.501] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.501] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.501] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.501] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 65
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49 len 0x4
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x49 len 0x4
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 65
[08-04 09:55:25.503] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.503] [37016 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.503] [37016 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.511] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 65
[08-04 09:55:25.511] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.511] [37218 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff38b0 len 0x4
[08-04 09:55:25.511] [37218 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.511] [37218 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.511] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 65
[08-04 09:55:25.511] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.511] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.511] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.511] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.511] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.511] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 66
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4c len 0x4
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4c len 0x4
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 66
[08-04 09:55:25.513] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.513] [37248 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.513] [37248 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.521] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 66
[08-04 09:55:25.521] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.521] [37450 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff55b0 len 0x4
[08-04 09:55:25.521] [37450 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.521] [37450 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.521] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 66
[08-04 09:55:25.521] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.521] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.521] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.521] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.521] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.521] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 67
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 67
[08-04 09:55:25.523] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.523] [37480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.523] [37480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.532] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 67
[08-04 09:55:25.532] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.532] [37682 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff72b0 len 0x4
[08-04 09:55:25.532] [37682 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.532] [37682 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.532] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 67
[08-04 09:55:25.532] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.532] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.532] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.532] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.532] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.532] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 68
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 68
[08-04 09:55:25.533] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.533] [37712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.533] [37712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.541] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 68
[08-04 09:55:25.541] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.541] [37888 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff8c70 len 0x4
[08-04 09:55:25.541] [37888 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.541] [37888 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.541] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 68
[08-04 09:55:25.541] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.541] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.541] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.541] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.541] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.541] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 69
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 69
[08-04 09:55:25.542] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.542] [37918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.542] [37918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.545] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 69
[08-04 09:55:25.545] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.545] [37994 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff99b0 len 0x4
[08-04 09:55:25.545] [37994 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.545] [37994 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.545] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 69
[08-04 09:55:25.545] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.545] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.545] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.545] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.545] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.545] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 70
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 70
[08-04 09:55:25.547] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.547] [38024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.547] [38024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.957] [44170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 70
[08-04 09:55:25.957] [44170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.957] [44170 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6014c30 len 0x4
[08-04 09:55:25.957] [44170 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.957] [44170 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.957] [44170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 70
[08-04 09:55:25.957] [44170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.957] [44170 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.957] [44170 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.957] [44170 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.957] [44170 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.957] [44170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 71
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x43 len 0x4
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x43 len 0x4
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 71
[08-04 09:55:25.959] [44198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.959] [44200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.959] [44200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:25.975] [44402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 71
[08-04 09:55:25.975] [44402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.975] [44402 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6016910 len 0x4
[08-04 09:55:25.975] [44402 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.975] [44402 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.975] [44402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 71
[08-04 09:55:25.975] [44402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.975] [44402 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.975] [44402 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.975] [44402 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.975] [44402 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.975] [44402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 72
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x50 len 0x4
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x50 len 0x4
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 72
[08-04 09:55:25.977] [44430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.977] [44432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.977] [44432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:25.992] [44634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 72
[08-04 09:55:25.992] [44634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:25.992] [44634 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6018610 len 0x4
[08-04 09:55:25.992] [44634 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.992] [44634 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:25.992] [44634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 72
[08-04 09:55:25.992] [44634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:25.992] [44634 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.992] [44634 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.992] [44634 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.992] [44634 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:25.992] [44634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 73
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x55 len 0x4
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x55 len 0x4
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 73
[08-04 09:55:25.994] [44662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:25.995] [44664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:25.995] [44664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.010] [44866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 73
[08-04 09:55:26.010] [44866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.010] [44866 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x601a310 len 0x4
[08-04 09:55:26.010] [44866 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.010] [44866 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.010] [44866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 73
[08-04 09:55:26.010] [44866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.010] [44866 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.010] [44866 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.010] [44866 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.010] [44866 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.010] [44866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 74
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 74
[08-04 09:55:26.012] [44894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.012] [44896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.012] [44896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.027] [45098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 74
[08-04 09:55:26.027] [45098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.027] [45098 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x601c010 len 0x4
[08-04 09:55:26.027] [45098 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.027] [45098 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.027] [45098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 74
[08-04 09:55:26.027] [45098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.027] [45098 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.027] [45098 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.027] [45098 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.027] [45098 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.027] [45098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 75
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x46 len 0x4
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x46 len 0x4
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 75
[08-04 09:55:26.029] [45126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.030] [45128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.030] [45128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.045] [45330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 75
[08-04 09:55:26.045] [45330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.045] [45330 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x602b0f0 len 0x4
[08-04 09:55:26.045] [45330 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.045] [45330 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.045] [45330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 75
[08-04 09:55:26.045] [45330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.045] [45330 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.045] [45330 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.045] [45330 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.045] [45330 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.045] [45330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 76
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x72 len 0x4
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x72 len 0x4
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 76
[08-04 09:55:26.047] [45358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.047] [45360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.047] [45360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.062] [45562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 76
[08-04 09:55:26.062] [45562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.062] [45562 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x602cdf0 len 0x4
[08-04 09:55:26.062] [45562 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.062] [45562 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.062] [45562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 76
[08-04 09:55:26.062] [45562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.062] [45562 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.062] [45562 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.062] [45562 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.062] [45562 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.062] [45562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 77
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 77
[08-04 09:55:26.064] [45590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.064] [45592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.064] [45592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.079] [45794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 77
[08-04 09:55:26.079] [45794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.079] [45794 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x602eaf0 len 0x4
[08-04 09:55:26.079] [45794 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.079] [45794 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.079] [45794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 77
[08-04 09:55:26.079] [45794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.079] [45794 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.079] [45794 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.079] [45794 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.079] [45794 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.079] [45794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 78
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x71 len 0x4
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x71 len 0x4
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 78
[08-04 09:55:26.081] [45822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.081] [45824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.081] [45824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.096] [46026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 78
[08-04 09:55:26.096] [46026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.096] [46026 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60307f0 len 0x4
[08-04 09:55:26.096] [46026 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.096] [46026 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.096] [46026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 78
[08-04 09:55:26.096] [46026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.096] [46026 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.096] [46026 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.096] [46026 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.096] [46026 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.096] [46026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 79
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 79
[08-04 09:55:26.098] [46054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.099] [46056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.099] [46056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.114] [46258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 79
[08-04 09:55:26.114] [46258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.114] [46258 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60324f0 len 0x4
[08-04 09:55:26.114] [46258 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.114] [46258 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.114] [46258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 79
[08-04 09:55:26.114] [46258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.114] [46258 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.114] [46258 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.114] [46258 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.114] [46258 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.114] [46258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 80
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 80
[08-04 09:55:26.116] [46286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.116] [46288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.116] [46288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.132] [46490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 80
[08-04 09:55:26.132] [46490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.132] [46490 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60341f0 len 0x4
[08-04 09:55:26.132] [46490 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.132] [46490 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.132] [46490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 80
[08-04 09:55:26.132] [46490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.132] [46490 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.132] [46490 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.132] [46490 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.132] [46490 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.132] [46490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 81
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 81
[08-04 09:55:26.134] [46518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.134] [46520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.134] [46520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.150] [46722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 81
[08-04 09:55:26.150] [46722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.150] [46722 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6035ef0 len 0x4
[08-04 09:55:26.150] [46722 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.150] [46722 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.150] [46722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 81
[08-04 09:55:26.150] [46722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.150] [46722 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.150] [46722 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.150] [46722 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.150] [46722 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.150] [46722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 82
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63 len 0x4
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x63 len 0x4
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 82
[08-04 09:55:26.152] [46750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.152] [46752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.152] [46752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.167] [46954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 82
[08-04 09:55:26.167] [46954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.167] [46954 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6037bf0 len 0x4
[08-04 09:55:26.167] [46954 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.167] [46954 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.167] [46954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 82
[08-04 09:55:26.167] [46954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.167] [46954 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.167] [46954 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.167] [46954 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.167] [46954 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.167] [46954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 83
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x79 len 0x4
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x79 len 0x4
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 83
[08-04 09:55:26.169] [46982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.170] [46984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.170] [46984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.185] [47186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 83
[08-04 09:55:26.185] [47186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.185] [47186 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60398f0 len 0x4
[08-04 09:55:26.185] [47186 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.185] [47186 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.185] [47186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 83
[08-04 09:55:26.185] [47186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.185] [47186 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.185] [47186 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.185] [47186 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.185] [47186 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.185] [47186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 84
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 84
[08-04 09:55:26.187] [47214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.187] [47216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.187] [47216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.202] [47418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 84
[08-04 09:55:26.202] [47418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.202] [47418 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x603b5f0 len 0x4
[08-04 09:55:26.202] [47418 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.202] [47418 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.202] [47418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 84
[08-04 09:55:26.202] [47418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.202] [47418 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.202] [47418 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.202] [47418 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.202] [47418 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.202] [47418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 85
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 85
[08-04 09:55:26.204] [47446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.205] [47448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.205] [47448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.220] [47650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 85
[08-04 09:55:26.220] [47650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.220] [47650 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x603d2f0 len 0x4
[08-04 09:55:26.220] [47650 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.220] [47650 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.220] [47650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 85
[08-04 09:55:26.220] [47650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.220] [47650 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.220] [47650 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.220] [47650 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.220] [47650 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.220] [47650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 86
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x36 len 0x4
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x36 len 0x4
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 86
[08-04 09:55:26.222] [47678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.222] [47680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.222] [47680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.237] [47882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 86
[08-04 09:55:26.237] [47882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.237] [47882 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x603eff0 len 0x4
[08-04 09:55:26.237] [47882 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.237] [47882 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.237] [47882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 86
[08-04 09:55:26.237] [47882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.237] [47882 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.237] [47882 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.237] [47882 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.237] [47882 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.237] [47882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 87
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 87
[08-04 09:55:26.239] [47910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.240] [47912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.240] [47912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.255] [48114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 87
[08-04 09:55:26.255] [48114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.255] [48114 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6040cf0 len 0x4
[08-04 09:55:26.255] [48114 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.255] [48114 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.255] [48114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 87
[08-04 09:55:26.255] [48114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.255] [48114 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.255] [48114 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.255] [48114 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.255] [48114 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.255] [48114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 88
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x39 len 0x4
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x39 len 0x4
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 88
[08-04 09:55:26.257] [48142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.257] [48144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.257] [48144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.272] [48346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 88
[08-04 09:55:26.272] [48346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.272] [48346 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60429f0 len 0x4
[08-04 09:55:26.272] [48346 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.272] [48346 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.272] [48346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 88
[08-04 09:55:26.272] [48346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.272] [48346 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.272] [48346 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.272] [48346 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.272] [48346 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.272] [48346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 89
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 89
[08-04 09:55:26.274] [48374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.275] [48376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.275] [48376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.290] [48578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 89
[08-04 09:55:26.290] [48578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.290] [48578 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60446f0 len 0x4
[08-04 09:55:26.290] [48578 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.290] [48578 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.290] [48578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 89
[08-04 09:55:26.290] [48578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.290] [48578 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.290] [48578 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.290] [48578 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.290] [48578 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.290] [48578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 90
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 90
[08-04 09:55:26.293] [48606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.293] [48608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.293] [48608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.309] [48810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 90
[08-04 09:55:26.309] [48810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.309] [48810 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60463f0 len 0x4
[08-04 09:55:26.309] [48810 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.309] [48810 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.309] [48810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 90
[08-04 09:55:26.309] [48810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.309] [48810 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.309] [48810 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.309] [48810 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.309] [48810 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.309] [48810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 91
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 91
[08-04 09:55:26.311] [48838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.311] [48840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.311] [48840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.326] [49042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 91
[08-04 09:55:26.326] [49042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.326] [49042 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60480f0 len 0x4
[08-04 09:55:26.326] [49042 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.326] [49042 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.326] [49042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 91
[08-04 09:55:26.326] [49042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.326] [49042 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.326] [49042 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.326] [49042 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.326] [49042 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.326] [49042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 92
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x48 len 0x4
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x48 len 0x4
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 92
[08-04 09:55:26.328] [49070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.328] [49072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.328] [49072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.343] [49274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 92
[08-04 09:55:26.343] [49274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.343] [49274 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6049df0 len 0x4
[08-04 09:55:26.343] [49274 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.343] [49274 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.343] [49274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 92
[08-04 09:55:26.343] [49274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.343] [49274 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.343] [49274 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.343] [49274 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.343] [49274 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.343] [49274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 93
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x7a len 0x4
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x7a len 0x4
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 93
[08-04 09:55:26.346] [49302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.346] [49304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.346] [49304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.361] [49506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 93
[08-04 09:55:26.361] [49506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.361] [49506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6122950 len 0x4
[08-04 09:55:26.361] [49506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.361] [49506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.361] [49506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 93
[08-04 09:55:26.361] [49506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.361] [49506 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.361] [49506 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.361] [49506 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.361] [49506 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.361] [49506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 94
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 94
[08-04 09:55:26.363] [49534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.363] [49536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.363] [49536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.376] [49712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 94
[08-04 09:55:26.376] [49712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.376] [49712 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6124310 len 0x4
[08-04 09:55:26.376] [49712 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.376] [49712 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.376] [49712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 94
[08-04 09:55:26.376] [49712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.376] [49712 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.376] [49712 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.376] [49712 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.376] [49712 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.376] [49712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 95
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 95
[08-04 09:55:26.378] [49740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.379] [49742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.379] [49742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.384] [49818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 95
[08-04 09:55:26.384] [49818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.384] [49818 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6125050 len 0x4
[08-04 09:55:26.384] [49818 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.384] [49818 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.384] [49818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 95
[08-04 09:55:26.384] [49818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.384] [49818 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.384] [49818 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.384] [49818 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.384] [49818 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.384] [49818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 96
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 96
[08-04 09:55:26.386] [49846 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.387] [49848 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.387] [49848 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.761] [54538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 96
[08-04 09:55:26.761] [54538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.761] [54538 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63cb4a0 len 0x4
[08-04 09:55:26.761] [54538 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.761] [54538 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.761] [54538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 96
[08-04 09:55:26.761] [54538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.761] [54538 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.761] [54538 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.761] [54538 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.761] [54538 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.761] [54538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 97
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x43 len 0x4
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x43 len 0x4
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 97
[08-04 09:55:26.763] [54566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.764] [54568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.764] [54568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.779] [54770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 97
[08-04 09:55:26.779] [54770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.779] [54770 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6122f90 len 0x4
[08-04 09:55:26.779] [54770 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.779] [54770 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.779] [54770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 97
[08-04 09:55:26.779] [54770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.779] [54770 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.779] [54770 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.779] [54770 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.779] [54770 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.779] [54770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 98
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x50 len 0x4
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x50 len 0x4
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 98
[08-04 09:55:26.781] [54798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.781] [54800 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.781] [54800 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.797] [55002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 98
[08-04 09:55:26.797] [55002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.797] [55002 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60287e0 len 0x4
[08-04 09:55:26.797] [55002 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.797] [55002 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.797] [55002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 98
[08-04 09:55:26.797] [55002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.797] [55002 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.797] [55002 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.797] [55002 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.797] [55002 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.797] [55002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 99
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x55 len 0x4
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x55 len 0x4
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 99
[08-04 09:55:26.799] [55030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.799] [55032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.799] [55032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.815] [55234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 99
[08-04 09:55:26.815] [55234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.815] [55234 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x602a4e0 len 0x4
[08-04 09:55:26.815] [55234 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.815] [55234 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.815] [55234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 99
[08-04 09:55:26.815] [55234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.815] [55234 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.815] [55234 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.815] [55234 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.815] [55234 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.815] [55234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 100
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 100
[08-04 09:55:26.817] [55262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.817] [55264 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.817] [55264 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.833] [55466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 100
[08-04 09:55:26.833] [55466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.833] [55466 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6026d60 len 0x4
[08-04 09:55:26.833] [55466 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.833] [55466 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.833] [55466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 100
[08-04 09:55:26.833] [55466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.833] [55466 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.833] [55466 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.833] [55466 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.833] [55466 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.833] [55466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 101
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x48 len 0x4
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x48 len 0x4
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 101
[08-04 09:55:26.835] [55494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.835] [55496 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.835] [55496 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.855] [55698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 101
[08-04 09:55:26.855] [55698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.855] [55698 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63c7e00 len 0x4
[08-04 09:55:26.855] [55698 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.855] [55698 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.855] [55698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 101
[08-04 09:55:26.855] [55698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.855] [55698 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.855] [55698 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.855] [55698 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.855] [55698 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.855] [55698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 102
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61 len 0x4
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x61 len 0x4
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 102
[08-04 09:55:26.858] [55726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.858] [55728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.858] [55728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.874] [55930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 102
[08-04 09:55:26.874] [55930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.874] [55930 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63c9b00 len 0x4
[08-04 09:55:26.874] [55930 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.874] [55930 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.874] [55930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 102
[08-04 09:55:26.874] [55930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.874] [55930 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.874] [55930 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.874] [55930 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.874] [55930 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.874] [55930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 103
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x72 len 0x4
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x72 len 0x4
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 103
[08-04 09:55:26.876] [55958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.876] [55960 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.876] [55960 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.891] [56162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 103
[08-04 09:55:26.891] [56162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.891] [56162 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5f9f5f0 len 0x4
[08-04 09:55:26.891] [56162 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.891] [56162 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.891] [56162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 103
[08-04 09:55:26.891] [56162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.891] [56162 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.891] [56162 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.891] [56162 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.891] [56162 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.891] [56162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 104
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x74 len 0x4
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x74 len 0x4
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 104
[08-04 09:55:26.894] [56190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.894] [56192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.894] [56192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.909] [56394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 104
[08-04 09:55:26.909] [56394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.909] [56394 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fa12f0 len 0x4
[08-04 09:55:26.909] [56394 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.909] [56394 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.909] [56394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 104
[08-04 09:55:26.909] [56394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.909] [56394 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.909] [56394 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.909] [56394 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.909] [56394 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.909] [56394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 105
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49 len 0x4
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x49 len 0x4
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 105
[08-04 09:55:26.911] [56422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.912] [56424 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.912] [56424 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.927] [56626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 105
[08-04 09:55:26.927] [56626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.927] [56626 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5f94d30 len 0x4
[08-04 09:55:26.927] [56626 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.927] [56626 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.927] [56626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 105
[08-04 09:55:26.927] [56626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.927] [56626 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.927] [56626 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.927] [56626 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.927] [56626 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.927] [56626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 106
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 106
[08-04 09:55:26.929] [56654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.929] [56656 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.929] [56656 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.945] [56858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 106
[08-04 09:55:26.945] [56858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.945] [56858 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5f96a30 len 0x4
[08-04 09:55:26.945] [56858 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.945] [56858 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.945] [56858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 106
[08-04 09:55:26.945] [56858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.945] [56858 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.945] [56858 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.945] [56858 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.945] [56858 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.945] [56858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 107
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 107
[08-04 09:55:26.947] [56886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.947] [56888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.947] [56888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.963] [57090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 107
[08-04 09:55:26.963] [57090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.963] [57090 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5f99000 len 0x4
[08-04 09:55:26.963] [57090 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.963] [57090 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.963] [57090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 107
[08-04 09:55:26.963] [57090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.963] [57090 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.963] [57090 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.963] [57090 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.963] [57090 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.963] [57090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 108
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 108
[08-04 09:55:26.965] [57118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.965] [57120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.965] [57120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:26.981] [57322 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 108
[08-04 09:55:26.981] [57322 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.981] [57322 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5f9ad00 len 0x4
[08-04 09:55:26.981] [57322 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.981] [57322 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.981] [57322 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 108
[08-04 09:55:26.981] [57322 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.981] [57322 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.981] [57322 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.981] [57322 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.981] [57322 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.981] [57322 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 109
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 109
[08-04 09:55:26.983] [57350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:26.983] [57352 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:26.983] [57352 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:26.998] [57554 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 109
[08-04 09:55:26.998] [57554 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:26.998] [57554 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5f9c9c0 len 0x4
[08-04 09:55:26.998] [57554 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:26.998] [57554 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:26.998] [57554 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 109
[08-04 09:55:26.998] [57554 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:26.998] [57554 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.998] [57554 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.998] [57554 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.998] [57554 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:26.998] [57554 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 110
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 110
[08-04 09:55:27.001] [57582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.001] [57584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.001] [57584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.014] [57760 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 110
[08-04 09:55:27.014] [57760 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.014] [57760 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60be420 len 0x4
[08-04 09:55:27.014] [57760 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.014] [57760 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.014] [57760 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 110
[08-04 09:55:27.014] [57760 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.014] [57760 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.014] [57760 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.014] [57760 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.014] [57760 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.014] [57760 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 111
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 111
[08-04 09:55:27.016] [57788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.016] [57790 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.016] [57790 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.022] [57866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 111
[08-04 09:55:27.022] [57866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.022] [57866 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60bf160 len 0x4
[08-04 09:55:27.022] [57866 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.022] [57866 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.022] [57866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 111
[08-04 09:55:27.022] [57866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.022] [57866 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.022] [57866 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.022] [57866 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.022] [57866 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.022] [57866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 112
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 112
[08-04 09:55:27.024] [57894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.024] [57896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.024] [57896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.427] [62654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 112
[08-04 09:55:27.427] [62654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.427] [62654 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff0fb0 len 0x4
[08-04 09:55:27.427] [62654 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.427] [62654 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.427] [62654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 112
[08-04 09:55:27.427] [62654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.427] [62654 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.427] [62654 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.427] [62654 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.427] [62654 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.427] [62654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 113
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x41 len 0x4
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x41 len 0x4
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 113
[08-04 09:55:27.430] [62682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.430] [62684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.430] [62684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.444] [62872 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 113
[08-04 09:55:27.444] [62872 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.444] [62872 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff2d00 len 0x4
[08-04 09:55:27.444] [62872 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.444] [62872 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.444] [62872 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 113
[08-04 09:55:27.444] [62872 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.444] [62872 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.444] [62872 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.444] [62872 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.444] [62872 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.444] [62872 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 114
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 114
[08-04 09:55:27.446] [62900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.446] [62902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.446] [62902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.456] [63032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 114
[08-04 09:55:27.456] [63032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.456] [63032 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff4100 len 0x4
[08-04 09:55:27.456] [63032 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.456] [63032 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.456] [63032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 114
[08-04 09:55:27.456] [63032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.456] [63032 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.456] [63032 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.456] [63032 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.456] [63032 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.456] [63032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 115
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 115
[08-04 09:55:27.458] [63060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.458] [63062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.459] [63062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.467] [63174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 115
[08-04 09:55:27.467] [63174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.467] [63174 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff52c0 len 0x4
[08-04 09:55:27.467] [63174 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.467] [63174 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.467] [63174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 115
[08-04 09:55:27.467] [63174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.467] [63174 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.467] [63174 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.467] [63174 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.467] [63174 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.467] [63174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 116
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 116
[08-04 09:55:27.469] [63202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.469] [63204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.469] [63204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.478] [63316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 116
[08-04 09:55:27.478] [63316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.478] [63316 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff6480 len 0x4
[08-04 09:55:27.478] [63316 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.478] [63316 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.478] [63316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 116
[08-04 09:55:27.478] [63316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.478] [63316 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.478] [63316 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.478] [63316 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.478] [63316 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.478] [63316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 117
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x74 len 0x4
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x74 len 0x4
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 117
[08-04 09:55:27.480] [63344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.480] [63346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.480] [63346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.488] [63458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 117
[08-04 09:55:27.488] [63458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.488] [63458 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff7640 len 0x4
[08-04 09:55:27.488] [63458 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.488] [63458 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.488] [63458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 117
[08-04 09:55:27.488] [63458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.488] [63458 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.488] [63458 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.488] [63458 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.488] [63458 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.488] [63458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 118
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 118
[08-04 09:55:27.490] [63486 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.491] [63488 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.491] [63488 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.499] [63600 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 118
[08-04 09:55:27.499] [63600 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.499] [63600 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff8800 len 0x4
[08-04 09:55:27.499] [63600 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.499] [63600 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.499] [63600 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 118
[08-04 09:55:27.499] [63600 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.499] [63600 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.499] [63600 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.499] [63600 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.499] [63600 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.499] [63600 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 119
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x73 len 0x4
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x73 len 0x4
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 119
[08-04 09:55:27.501] [63628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.501] [63630 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.501] [63630 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.510] [63742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 119
[08-04 09:55:27.510] [63742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.510] [63742 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ff99c0 len 0x4
[08-04 09:55:27.510] [63742 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.510] [63742 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.510] [63742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 119
[08-04 09:55:27.510] [63742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.510] [63742 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.510] [63742 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.510] [63742 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.510] [63742 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.510] [63742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 120
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x74 len 0x4
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x74 len 0x4
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 120
[08-04 09:55:27.512] [63770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.512] [63772 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.512] [63772 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.521] [63884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 120
[08-04 09:55:27.521] [63884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.521] [63884 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ffab80 len 0x4
[08-04 09:55:27.521] [63884 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.521] [63884 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.521] [63884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 120
[08-04 09:55:27.521] [63884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.521] [63884 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.521] [63884 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.521] [63884 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.521] [63884 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.521] [63884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 121
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x73 len 0x4
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x73 len 0x4
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 121
[08-04 09:55:27.523] [63912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.523] [63914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.523] [63914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.532] [64026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 121
[08-04 09:55:27.532] [64026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.532] [64026 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ffbd40 len 0x4
[08-04 09:55:27.532] [64026 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.532] [64026 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.532] [64026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 121
[08-04 09:55:27.532] [64026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.532] [64026 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.532] [64026 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.532] [64026 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.532] [64026 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.532] [64026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 122
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 122
[08-04 09:55:27.534] [64054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.534] [64056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.534] [64056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.542] [64168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 122
[08-04 09:55:27.542] [64168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.542] [64168 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ffcf00 len 0x4
[08-04 09:55:27.542] [64168 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.542] [64168 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.542] [64168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 122
[08-04 09:55:27.542] [64168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.542] [64168 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.542] [64168 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.542] [64168 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.542] [64168 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.542] [64168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 123
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x66 len 0x4
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x66 len 0x4
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 123
[08-04 09:55:27.545] [64196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.545] [64198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.545] [64198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.553] [64310 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 123
[08-04 09:55:27.553] [64310 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.553] [64310 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5ffe0c0 len 0x4
[08-04 09:55:27.553] [64310 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.553] [64310 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.553] [64310 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 123
[08-04 09:55:27.553] [64310 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.553] [64310 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.553] [64310 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.553] [64310 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.553] [64310 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.553] [64310 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 124
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 124
[08-04 09:55:27.555] [64338 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.556] [64340 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.556] [64340 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.564] [64452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 124
[08-04 09:55:27.564] [64452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.564] [64452 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x5fff280 len 0x4
[08-04 09:55:27.564] [64452 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.564] [64452 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.564] [64452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 124
[08-04 09:55:27.564] [64452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.564] [64452 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.564] [64452 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.564] [64452 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.564] [64452 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.564] [64452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 125
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 125
[08-04 09:55:27.566] [64480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.566] [64482 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.566] [64482 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.575] [64594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 125
[08-04 09:55:27.575] [64594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.575] [64594 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x604b660 len 0x4
[08-04 09:55:27.575] [64594 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.575] [64594 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.575] [64594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 125
[08-04 09:55:27.575] [64594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.575] [64594 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.575] [64594 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.575] [64594 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.575] [64594 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.575] [64594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 126
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 126
[08-04 09:55:27.577] [64622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.577] [64624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.577] [64624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.586] [64736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 126
[08-04 09:55:27.586] [64736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.586] [64736 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x604c820 len 0x4
[08-04 09:55:27.586] [64736 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.586] [64736 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.586] [64736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 126
[08-04 09:55:27.586] [64736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.586] [64736 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.586] [64736 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.586] [64736 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.586] [64736 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.586] [64736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 127
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x73 len 0x4
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x73 len 0x4
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 127
[08-04 09:55:27.588] [64764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.588] [64766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.588] [64766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.596] [64878 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 127
[08-04 09:55:27.596] [64878 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.596] [64878 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x604d9e0 len 0x4
[08-04 09:55:27.596] [64878 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.596] [64878 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.596] [64878 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 127
[08-04 09:55:27.596] [64878 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.596] [64878 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.596] [64878 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.596] [64878 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.596] [64878 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.596] [64878 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.598] [64906 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.598] [64906 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.598] [64906 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.598] [64906 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.598] [64906 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 128
[08-04 09:55:27.598] [64906 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.598] [64906 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x68 len 0x4
[08-04 09:55:27.598] [64906 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.599] [64906 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x68 len 0x4
[08-04 09:55:27.599] [64906 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 128
[08-04 09:55:27.599] [64906 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.599] [64908 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.599] [64908 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.607] [65020 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 128
[08-04 09:55:27.607] [65020 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.607] [65020 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x604eba0 len 0x4
[08-04 09:55:27.607] [65020 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.607] [65020 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.607] [65020 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 128
[08-04 09:55:27.607] [65020 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.607] [65020 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.607] [65020 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.607] [65020 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.607] [65020 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.607] [65020 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 129
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 129
[08-04 09:55:27.609] [65048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.610] [65050 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.610] [65050 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.618] [65162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 129
[08-04 09:55:27.618] [65162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.618] [65162 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x604fd60 len 0x4
[08-04 09:55:27.618] [65162 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.618] [65162 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.618] [65162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 129
[08-04 09:55:27.618] [65162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.618] [65162 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.618] [65162 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.618] [65162 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.618] [65162 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.618] [65162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 130
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 130
[08-04 09:55:27.620] [65190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.621] [65192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.621] [65192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.630] [65320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 130
[08-04 09:55:27.630] [65320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.630] [65320 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6051120 len 0x4
[08-04 09:55:27.630] [65320 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.630] [65320 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.630] [65320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 130
[08-04 09:55:27.630] [65320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.630] [65320 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.630] [65320 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.630] [65320 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.630] [65320 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.630] [65320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 131
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 131
[08-04 09:55:27.632] [65348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.632] [65350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.632] [65350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.636] [65402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 131
[08-04 09:55:27.636] [65402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.636] [65402 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6051b60 len 0x4
[08-04 09:55:27.636] [65402 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.636] [65402 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.636] [65402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 131
[08-04 09:55:27.636] [65402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.636] [65402 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.636] [65402 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.636] [65402 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.636] [65402 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.636] [65402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 132
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 132
[08-04 09:55:27.638] [65430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.638] [65432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.638] [65432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.673] [65890 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 132
[08-04 09:55:27.673] [65890 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.673] [65890 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6055860 len 0x4
[08-04 09:55:27.673] [65890 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.673] [65890 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.673] [65890 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 132
[08-04 09:55:27.673] [65890 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.673] [65890 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.673] [65890 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.673] [65890 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.673] [65890 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.673] [65890 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.675] [65918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.675] [65918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.675] [65918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.675] [65918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.675] [65918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 133
[08-04 09:55:27.675] [65918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.675] [65918 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-04 09:55:27.675] [65918 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.676] [65918 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.676] [65918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 133
[08-04 09:55:27.676] [65918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.676] [65920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.676] [65920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.682] [66004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 133
[08-04 09:55:27.682] [66004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.682] [66004 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60566a0 len 0x4
[08-04 09:55:27.682] [66004 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.682] [66004 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.682] [66004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 133
[08-04 09:55:27.682] [66004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.682] [66004 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.682] [66004 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.682] [66004 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.682] [66004 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.682] [66004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 134
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 134
[08-04 09:55:27.684] [66032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.684] [66034 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.684] [66034 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.689] [66098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 134
[08-04 09:55:27.689] [66098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.689] [66098 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6057260 len 0x4
[08-04 09:55:27.689] [66098 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.689] [66098 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.689] [66098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 134
[08-04 09:55:27.689] [66098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.689] [66098 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.689] [66098 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.689] [66098 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.689] [66098 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.689] [66098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 135
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 135
[08-04 09:55:27.691] [66126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.691] [66128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.691] [66128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.696] [66192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 135
[08-04 09:55:27.696] [66192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.696] [66192 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6057e20 len 0x4
[08-04 09:55:27.696] [66192 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.696] [66192 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.696] [66192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 135
[08-04 09:55:27.696] [66192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.696] [66192 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.696] [66192 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.696] [66192 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.696] [66192 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.696] [66192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 136
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 136
[08-04 09:55:27.698] [66220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.698] [66222 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.698] [66222 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.703] [66286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 136
[08-04 09:55:27.703] [66286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.703] [66286 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60589e0 len 0x4
[08-04 09:55:27.703] [66286 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.703] [66286 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.703] [66286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 136
[08-04 09:55:27.703] [66286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.703] [66286 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.703] [66286 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.703] [66286 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.703] [66286 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.703] [66286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 137
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 137
[08-04 09:55:27.705] [66314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.705] [66316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.705] [66316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.710] [66380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 137
[08-04 09:55:27.710] [66380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.710] [66380 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x60595a0 len 0x4
[08-04 09:55:27.710] [66380 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.710] [66380 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.710] [66380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 137
[08-04 09:55:27.710] [66380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.710] [66380 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.710] [66380 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.710] [66380 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.710] [66380 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.710] [66380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 138
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 138
[08-04 09:55:27.712] [66408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.712] [66410 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.712] [66410 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.717] [66474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 138
[08-04 09:55:27.717] [66474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.717] [66474 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x605a160 len 0x4
[08-04 09:55:27.717] [66474 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.717] [66474 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.717] [66474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 138
[08-04 09:55:27.717] [66474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.717] [66474 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.717] [66474 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.717] [66474 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.717] [66474 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.717] [66474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 139
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 139
[08-04 09:55:27.719] [66502 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.719] [66504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.719] [66504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.724] [66568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 139
[08-04 09:55:27.724] [66568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.724] [66568 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x605ad20 len 0x4
[08-04 09:55:27.724] [66568 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.724] [66568 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.724] [66568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 139
[08-04 09:55:27.724] [66568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.724] [66568 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.724] [66568 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.724] [66568 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.724] [66568 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.724] [66568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 140
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 140
[08-04 09:55:27.726] [66596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.726] [66598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.726] [66598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
[08-04 09:55:27.731] [66662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb060c4ea0, reqid: 140
[08-04 09:55:27.731] [66662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.731] [66662 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x605b8e0 len 0x4
[08-04 09:55:27.731] [66662 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.731] [66662 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.731] [66662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 140
[08-04 09:55:27.731] [66662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.731] [66662 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.731] [66662 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.731] [66662 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.731] [66662 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.731] [66662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 141
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 141
[08-04 09:55:27.733] [66690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.733] [66692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.733] [66692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.738] [66756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 141
[08-04 09:55:27.738] [66756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.738] [66756 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x605c4a0 len 0x4
[08-04 09:55:27.738] [66756 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.738] [66756 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.738] [66756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 141
[08-04 09:55:27.738] [66756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.738] [66756 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.738] [66756 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.738] [66756 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.738] [66756 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.738] [66756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb041758b0, reqid: 142
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb060c4ea0
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb041758b0, wt: 0x7ffb060c4ea0
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 142
[08-04 09:55:27.740] [66784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.740] [66786 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.740] [66786 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb041758b0
[08-04 09:55:27.746] [66870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7ffb041758b0, reqid: 142
[08-04 09:55:27.746] [66870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 09:55:27.746] [66870 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x605d2e0 len 0x4
[08-04 09:55:27.746] [66870 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.746] [66870 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 09:55:27.746] [66870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 142
[08-04 09:55:27.746] [66870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 09:55:27.746] [66870 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.746] [66870 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.746] [66870 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.746] [66870 ns] axi2tlm: rdata.write 0x0
[08-04 09:55:27.746] [66870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 09:55:27.748] [66898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7ffb041758b0
[08-04 09:55:27.748] [66898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 09:55:27.748] [66898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 09:55:27.748] [66898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 09:55:27.748] [66898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7ffb060c4ea0, reqid: 143
[08-04 09:55:27.748] [66898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7ffb060c4ea0, wt: 0x7ffb041758b0
[08-04 09:55:27.748] [66898 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 09:55:27.748] [66898 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 09:55:27.749] [66898 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 09:55:27.749] [66898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 143
[08-04 09:55:27.749] [66898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 09:55:27.749] [66900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 09:55:27.749] [66900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7ffb060c4ea0
