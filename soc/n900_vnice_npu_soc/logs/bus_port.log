[08-04 10:43:16.274] [0 s] axi-master n900_vnice_npu_soc.n900_vnice.mem__128_axi_m binding device n900_vnice_npu_soc.bus.s_0
[08-04 10:43:16.276] [0 s] nice-master n900_vnice_npu_soc.n900_vnice.nice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.nice
[08-04 10:43:16.278] [0 s] vnice-master n900_vnice_npu_soc.n900_vnice.vnice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.vnice
[08-04 10:43:16.280] [0 s] axi-slave stub n900_vnice_npu_soc.n900_vnice.Vn900_core_rams port n900_vnice_npu_soc.n900_vnice.slv__64_axi_s
[08-04 10:43:16.282] [0 s] ------- load n900_vnice_npu_soc.bus route rules: -------
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_0: slave0 to master0 n900_vnice_npu_soc.acc0 addr_range: start 0x100000 -- end 0x1fffff, length 0x100000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_1: slave0 to master1 n900_vnice_npu_soc.mrom addr_range: start 0x0 -- end 0xfffff, length 0x100000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_2: slave0 to master2 n900_vnice_npu_soc.gpio addr_range: start 0x10012000 -- end 0x10012fff, length 0x1000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_3: slave0 to master3 n900_vnice_npu_soc.uart addr_range: start 0x10013000 -- end 0x10013fff, length 0x1000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_4: slave0 to master4 n900_vnice_npu_soc.qspi0 addr_range: start 0x10014000 -- end 0x10023fff, length 0x10000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_5: slave0 to master5 n900_vnice_npu_soc.qspi1 addr_range: start 0x60000000 -- end 0x6effffff, length 0xf000000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_6: slave0 to master6 n900_vnice_npu_soc.hole addr_range: start 0x50000000 -- end 0x5fedffff, length 0xfee0000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_7: slave0 to master7 n900_vnice_npu_soc.xip addr_range: start 0x20000000 -- end 0x3fffffff, length 0x20000000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_8: slave0 to master8 n900_vnice_npu_soc.ddr addr_range: start 0x70180000 -- end 0x9017ffff, length 0x20000000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_9: slave0 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_10: slave0 to master10 n900_vnice_npu_soc.n900_vnice addr_range: start 0x70000000 -- end 0x7017ffff, length 0x180000
[08-04 10:43:16.282] [0 s] n900_vnice_npu_soc.bus rule_11: slave1 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[08-04 10:43:17.430] [0 s] acc0 ------ load_images -------
[08-04 10:43:17.430] [0 s] acc1 ------ load_images -------
[08-04 10:43:17.430] [0 s] bus ------ load_images -------
[08-04 10:43:17.430] [0 s] ddr ------ load_images -------
[08-04 10:43:17.430] [0 s] gpio ------ load_images -------
[08-04 10:43:17.430] [0 s] hole ------ load_images -------
[08-04 10:43:17.430] [0 s] mrom ------ load_images -------
[08-04 10:43:17.430] [0 s] n900_vnice ------ load_images -------
[08-04 10:43:17.430] [0 s] nice_remote_adapter ------ load_images -------
[08-04 10:43:17.430] [0 s] qspi0 ------ load_images -------
[08-04 10:43:17.430] [0 s] qspi1 ------ load_images -------
[08-04 10:43:17.430] [0 s] xip ------ load_images -------
[08-04 10:43:17.440] [40 ns] mem__128_axi_m_0_bridge ------ load_images -------
[08-04 10:43:17.440] [40 ns] load_images: ./fw/test_noc.elf offset 0x0
[08-04 10:43:17.440] [40 ns] load_elf: file ./fw/test_noc.elf with 3 segments
[08-04 10:43:17.440] [40 ns] load_elf: segment 0x70000000         .. 0x70001f97        
[08-04 10:43:17.441] [40 ns] load_elf: segment 0x70100000         .. 0x70100dff        
[08-04 10:43:17.441] [40 ns] load_elf: segment 0x70100e00         .. 0x701015ff        
[08-04 10:43:17.441] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:17.831] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c64403c0
[08-04 10:43:17.831] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:17.831] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:17.831] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:17.831] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013018, tr: 0x7f16c60c4ea0, reqid: 0
[08-04 10:43:17.831] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013018, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c64403c0
[08-04 10:43:17.831] [6022 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x18 data 0x0 len 0x4
[08-04 10:43:17.831] [6022 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x18 len 0x4  in pv mode
[08-04 10:43:17.832] [6022 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013018 data 0x0 len 0x4
[08-04 10:43:17.832] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 0
[08-04 10:43:17.832] [6022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:17.832] [6024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:17.832] [6024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:17.835] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013008, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 0
[08-04 10:43:17.835] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:17.835] [6048 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x8 data 0xc60005e0 len 0x4
[08-04 10:43:17.835] [6048 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x8 len 0x4  in pv mode
[08-04 10:43:17.835] [6048 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013008 data 0x0 len 0x4
[08-04 10:43:17.835] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 0
[08-04 10:43:17.835] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:17.835] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:17.835] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:17.835] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:17.835] [6048 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:17.835] [6048 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:17.843] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013008, tr: 0x7f16c60c4ea0, reqid: 1
[08-04 10:43:17.843] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:17.843] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:17.843] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:17.843] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:17.844] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013008, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:17.844] [6076 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x8 data 0x1 len 0x4
[08-04 10:43:17.844] [6076 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x8 len 0x4  in pv mode
[08-04 10:43:17.844] [6076 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013008 data 0x1 len 0x4
[08-04 10:43:17.844] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 1
[08-04 10:43:17.844] [6076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:17.844] [6078 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:17.844] [6078 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:17.846] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x1001300c, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 1
[08-04 10:43:17.846] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:17.846] [6104 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0xc data 0xc6000d70 len 0x4
[08-04 10:43:17.846] [6104 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0xc len 0x4  in pv mode
[08-04 10:43:17.846] [6104 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x1001300c data 0x0 len 0x4
[08-04 10:43:17.846] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 1
[08-04 10:43:17.846] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:17.846] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:17.846] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:17.846] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:17.846] [6104 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:17.846] [6104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 1001300c, tr: 0x7f16c60c4ea0, reqid: 2
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x1001300c, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0xc data 0x1 len 0x4
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0xc len 0x4  in pv mode
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x1001300c data 0x1 len 0x4
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 2
[08-04 10:43:17.848] [6132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:17.848] [6134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:17.848] [6134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:18.717] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 2
[08-04 10:43:18.717] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.717] [17622 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5fec160 len 0x4
[08-04 10:43:18.717] [17622 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.717] [17622 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.717] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 2
[08-04 10:43:18.717] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.717] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.717] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.717] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.717] [17622 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.717] [17622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 3
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4e len 0x4
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4e len 0x4
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 3
[08-04 10:43:18.719] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.719] [17652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.719] [17652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:18.739] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 3
[08-04 10:43:18.739] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.739] [17854 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5fede60 len 0x4
[08-04 10:43:18.739] [17854 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.739] [17854 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.739] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 3
[08-04 10:43:18.739] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.739] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.739] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.739] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.739] [17854 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.739] [17854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 4
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 4
[08-04 10:43:18.742] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.742] [17884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.742] [17884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:18.757] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 4
[08-04 10:43:18.757] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.757] [18086 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc61224f0 len 0x4
[08-04 10:43:18.757] [18086 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.757] [18086 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.757] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 4
[08-04 10:43:18.757] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.757] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.757] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.757] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.757] [18086 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.757] [18086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 5
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63 len 0x4
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x63 len 0x4
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 5
[08-04 10:43:18.760] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.760] [18116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.760] [18116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:18.775] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 5
[08-04 10:43:18.775] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.775] [18318 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc61241f0 len 0x4
[08-04 10:43:18.775] [18318 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.775] [18318 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.775] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 5
[08-04 10:43:18.775] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.775] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.775] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.775] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.775] [18318 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.775] [18318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 6
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 6
[08-04 10:43:18.778] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.778] [18348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.778] [18348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:18.793] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 6
[08-04 10:43:18.793] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.793] [18550 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6125ef0 len 0x4
[08-04 10:43:18.793] [18550 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.793] [18550 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.793] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 6
[08-04 10:43:18.793] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.793] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.793] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.793] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.793] [18550 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.793] [18550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 7
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 7
[08-04 10:43:18.796] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.796] [18580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.796] [18580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:18.811] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 7
[08-04 10:43:18.811] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.811] [18782 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6127bf0 len 0x4
[08-04 10:43:18.811] [18782 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.811] [18782 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.811] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 7
[08-04 10:43:18.811] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.811] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.811] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.811] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.811] [18782 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.811] [18782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 8
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 8
[08-04 10:43:18.813] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.814] [18812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.814] [18812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:18.829] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 8
[08-04 10:43:18.829] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.829] [19014 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc61298f0 len 0x4
[08-04 10:43:18.829] [19014 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.829] [19014 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.829] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 8
[08-04 10:43:18.829] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.829] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.829] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.829] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.829] [19014 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.829] [19014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 9
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 9
[08-04 10:43:18.831] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.832] [19044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.832] [19044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:18.847] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 9
[08-04 10:43:18.847] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.847] [19246 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc612b5f0 len 0x4
[08-04 10:43:18.847] [19246 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.847] [19246 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.847] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 9
[08-04 10:43:18.847] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.847] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.847] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.847] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.847] [19246 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.847] [19246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 10
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x53 len 0x4
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x53 len 0x4
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 10
[08-04 10:43:18.849] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.849] [19276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.849] [19276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:18.865] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 10
[08-04 10:43:18.865] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.865] [19478 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc612d2f0 len 0x4
[08-04 10:43:18.865] [19478 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.865] [19478 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.865] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 10
[08-04 10:43:18.865] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.865] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.865] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.865] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.865] [19478 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.865] [19478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 11
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 11
[08-04 10:43:18.867] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.867] [19508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.867] [19508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:18.882] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 11
[08-04 10:43:18.882] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.882] [19710 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc612eff0 len 0x4
[08-04 10:43:18.882] [19710 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.882] [19710 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.882] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 11
[08-04 10:43:18.882] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.882] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.882] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.882] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.882] [19710 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.882] [19710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 12
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4b len 0x4
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4b len 0x4
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 12
[08-04 10:43:18.885] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.885] [19740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.885] [19740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:18.900] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 12
[08-04 10:43:18.900] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.900] [19942 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6130cf0 len 0x4
[08-04 10:43:18.900] [19942 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.900] [19942 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.900] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 12
[08-04 10:43:18.900] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.900] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.900] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.900] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.900] [19942 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.900] [19942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 13
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 13
[08-04 10:43:18.902] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.903] [19972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.903] [19972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:18.918] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 13
[08-04 10:43:18.918] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.918] [20174 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc61329f0 len 0x4
[08-04 10:43:18.918] [20174 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.918] [20174 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.918] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 13
[08-04 10:43:18.918] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.918] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.918] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.918] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.918] [20174 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.918] [20174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 14
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x42 len 0x4
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x42 len 0x4
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 14
[08-04 10:43:18.920] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.920] [20204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.920] [20204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:18.936] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 14
[08-04 10:43:18.936] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.936] [20406 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc61346f0 len 0x4
[08-04 10:43:18.936] [20406 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.936] [20406 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.936] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 14
[08-04 10:43:18.936] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.936] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.936] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.936] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.936] [20406 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.936] [20406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 15
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 15
[08-04 10:43:18.938] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.938] [20436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.938] [20436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:18.953] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 15
[08-04 10:43:18.953] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.953] [20638 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc61363f0 len 0x4
[08-04 10:43:18.953] [20638 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.953] [20638 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.953] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 15
[08-04 10:43:18.953] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.953] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.953] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.953] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.953] [20638 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.953] [20638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 16
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 16
[08-04 10:43:18.958] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.958] [20668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.958] [20668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:18.973] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 16
[08-04 10:43:18.973] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.973] [20870 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc61380f0 len 0x4
[08-04 10:43:18.973] [20870 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.973] [20870 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.973] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 16
[08-04 10:43:18.973] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.973] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.973] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.973] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.973] [20870 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.973] [20870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 17
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 17
[08-04 10:43:18.976] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.976] [20900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.976] [20900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:18.991] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 17
[08-04 10:43:18.991] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:18.991] [21102 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6139df0 len 0x4
[08-04 10:43:18.991] [21102 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.991] [21102 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:18.991] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 17
[08-04 10:43:18.991] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:18.991] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.991] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.991] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.991] [21102 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:18.991] [21102 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 18
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 18
[08-04 10:43:18.993] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:18.994] [21132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:18.994] [21132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.009] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 18
[08-04 10:43:19.009] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.009] [21334 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc613baf0 len 0x4
[08-04 10:43:19.009] [21334 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.009] [21334 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.009] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 18
[08-04 10:43:19.009] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.009] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.009] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.009] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.009] [21334 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.009] [21334 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 19
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 19
[08-04 10:43:19.011] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.011] [21364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.011] [21364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.026] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 19
[08-04 10:43:19.026] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.026] [21566 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc613d7f0 len 0x4
[08-04 10:43:19.026] [21566 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.026] [21566 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.026] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 19
[08-04 10:43:19.026] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.026] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.026] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.026] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.026] [21566 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.026] [21566 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 20
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x54 len 0x4
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x54 len 0x4
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 20
[08-04 10:43:19.029] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.029] [21596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.029] [21596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.044] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 20
[08-04 10:43:19.044] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.044] [21798 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc613f4f0 len 0x4
[08-04 10:43:19.044] [21798 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.044] [21798 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.044] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 20
[08-04 10:43:19.044] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.044] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.044] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.044] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.044] [21798 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.044] [21798 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 21
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 21
[08-04 10:43:19.046] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.046] [21828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.046] [21828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.062] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 21
[08-04 10:43:19.062] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.062] [22030 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc61411f0 len 0x4
[08-04 10:43:19.062] [22030 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.062] [22030 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.062] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 21
[08-04 10:43:19.062] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.062] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.062] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.062] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.062] [22030 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.062] [22030 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 22
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6d len 0x4
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6d len 0x4
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 22
[08-04 10:43:19.066] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.066] [22060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.066] [22060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.082] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 22
[08-04 10:43:19.082] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.082] [22262 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6037330 len 0x4
[08-04 10:43:19.082] [22262 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.082] [22262 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.082] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 22
[08-04 10:43:19.082] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.082] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.082] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.082] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.082] [22262 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.082] [22262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 23
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 23
[08-04 10:43:19.084] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.084] [22292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.084] [22292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.100] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 23
[08-04 10:43:19.100] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.100] [22494 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6039030 len 0x4
[08-04 10:43:19.100] [22494 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.100] [22494 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.100] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 23
[08-04 10:43:19.100] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.100] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.100] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.100] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.100] [22494 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.100] [22494 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 24
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 24
[08-04 10:43:19.103] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.103] [22524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.103] [22524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.119] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 24
[08-04 10:43:19.119] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.119] [22726 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc603ad30 len 0x4
[08-04 10:43:19.119] [22726 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.119] [22726 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.119] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 24
[08-04 10:43:19.119] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.119] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.119] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.119] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.119] [22726 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.119] [22726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 25
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 25
[08-04 10:43:19.121] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.121] [22756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.121] [22756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.137] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 25
[08-04 10:43:19.137] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.137] [22958 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc603ca30 len 0x4
[08-04 10:43:19.137] [22958 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.137] [22958 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.137] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 25
[08-04 10:43:19.137] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.137] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.137] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.137] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.137] [22958 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.137] [22958 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 26
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x41 len 0x4
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x41 len 0x4
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 26
[08-04 10:43:19.139] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.139] [22988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.139] [22988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.155] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 26
[08-04 10:43:19.155] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.155] [23190 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc603e730 len 0x4
[08-04 10:43:19.155] [23190 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.155] [23190 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.155] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 26
[08-04 10:43:19.155] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.155] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.155] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.155] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.155] [23190 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.155] [23190 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 27
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 27
[08-04 10:43:19.157] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.157] [23220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.157] [23220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.173] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 27
[08-04 10:43:19.173] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.173] [23422 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6040430 len 0x4
[08-04 10:43:19.173] [23422 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.173] [23422 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.173] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 27
[08-04 10:43:19.173] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.173] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.173] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.173] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.173] [23422 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.173] [23422 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 28
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x67 len 0x4
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x67 len 0x4
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 28
[08-04 10:43:19.175] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.175] [23452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.175] [23452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.191] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 28
[08-04 10:43:19.191] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.191] [23654 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6042130 len 0x4
[08-04 10:43:19.191] [23654 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.191] [23654 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.191] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 28
[08-04 10:43:19.191] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.191] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.191] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.191] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.191] [23654 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.191] [23654 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 29
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 29
[08-04 10:43:19.193] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.193] [23684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.194] [23684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.210] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 29
[08-04 10:43:19.210] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.210] [23886 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6043e30 len 0x4
[08-04 10:43:19.210] [23886 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.210] [23886 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.210] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 29
[08-04 10:43:19.210] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.210] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.210] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.210] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.210] [23886 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.210] [23886 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 30
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 30
[08-04 10:43:19.212] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.212] [23916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.212] [23916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.228] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 30
[08-04 10:43:19.228] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.228] [24118 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6045b30 len 0x4
[08-04 10:43:19.228] [24118 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.228] [24118 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.228] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 30
[08-04 10:43:19.228] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.228] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.228] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.228] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.228] [24118 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.228] [24118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 31
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x34 len 0x4
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x34 len 0x4
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 31
[08-04 10:43:19.231] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.231] [24148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.231] [24148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.248] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 31
[08-04 10:43:19.248] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.248] [24350 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6047830 len 0x4
[08-04 10:43:19.248] [24350 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.248] [24350 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.248] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 31
[08-04 10:43:19.248] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.248] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.248] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.248] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.248] [24350 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.248] [24350 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 32
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 32
[08-04 10:43:19.250] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.250] [24380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.250] [24380 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.267] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 32
[08-04 10:43:19.267] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.267] [24582 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6049530 len 0x4
[08-04 10:43:19.267] [24582 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.267] [24582 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.267] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 32
[08-04 10:43:19.267] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.267] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.267] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.267] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.267] [24582 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.267] [24582 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 33
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 33
[08-04 10:43:19.269] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.269] [24612 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.269] [24612 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.286] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 33
[08-04 10:43:19.286] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.286] [24814 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc604b230 len 0x4
[08-04 10:43:19.286] [24814 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.286] [24814 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.286] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 33
[08-04 10:43:19.286] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.286] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.286] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.286] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.286] [24814 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.286] [24814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 34
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 34
[08-04 10:43:19.288] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.288] [24844 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.288] [24844 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.305] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 34
[08-04 10:43:19.305] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.305] [25046 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc604cf30 len 0x4
[08-04 10:43:19.305] [25046 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.305] [25046 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.305] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 34
[08-04 10:43:19.305] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.305] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.305] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.305] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.305] [25046 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.305] [25046 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.307] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.307] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.307] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.307] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.307] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 35
[08-04 10:43:19.307] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.307] [25074 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-04 10:43:19.307] [25074 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.308] [25074 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-04 10:43:19.308] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 35
[08-04 10:43:19.308] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.308] [25076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.308] [25076 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.324] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 35
[08-04 10:43:19.324] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.324] [25278 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc604ec30 len 0x4
[08-04 10:43:19.324] [25278 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.324] [25278 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.324] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 35
[08-04 10:43:19.324] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.324] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.324] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.324] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.324] [25278 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.324] [25278 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.326] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.326] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.326] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.326] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.327] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 36
[08-04 10:43:19.327] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.327] [25306 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-04 10:43:19.327] [25306 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.327] [25306 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-04 10:43:19.327] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 36
[08-04 10:43:19.327] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.327] [25308 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.327] [25308 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.343] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 36
[08-04 10:43:19.343] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.343] [25510 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6050930 len 0x4
[08-04 10:43:19.343] [25510 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.343] [25510 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.343] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 36
[08-04 10:43:19.343] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.344] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.344] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.344] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.344] [25510 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.344] [25510 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 37
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x2c len 0x4
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x2c len 0x4
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 37
[08-04 10:43:19.346] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.346] [25540 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.346] [25540 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.363] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 37
[08-04 10:43:19.363] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.363] [25742 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6052630 len 0x4
[08-04 10:43:19.363] [25742 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.363] [25742 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.363] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 37
[08-04 10:43:19.363] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.363] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.363] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.363] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.363] [25742 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.363] [25742 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 38
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 38
[08-04 10:43:19.365] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.365] [25772 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.365] [25772 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.382] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 38
[08-04 10:43:19.382] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.382] [25974 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6054330 len 0x4
[08-04 10:43:19.382] [25974 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.382] [25974 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.382] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 38
[08-04 10:43:19.382] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.382] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.382] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.382] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.382] [25974 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.382] [25974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 39
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 39
[08-04 10:43:19.384] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.384] [26004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.385] [26004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.401] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 39
[08-04 10:43:19.401] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.401] [26206 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6056030 len 0x4
[08-04 10:43:19.401] [26206 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.401] [26206 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.401] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 39
[08-04 10:43:19.401] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.401] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.401] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.401] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.401] [26206 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.401] [26206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.403] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.403] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.403] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.403] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.403] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 40
[08-04 10:43:19.403] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.404] [26234 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x39 len 0x4
[08-04 10:43:19.404] [26234 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.404] [26234 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x39 len 0x4
[08-04 10:43:19.404] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 40
[08-04 10:43:19.404] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.404] [26236 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.404] [26236 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.420] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 40
[08-04 10:43:19.420] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.420] [26438 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6057d30 len 0x4
[08-04 10:43:19.420] [26438 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.420] [26438 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.420] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 40
[08-04 10:43:19.420] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.420] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.420] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.420] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.420] [26438 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.420] [26438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 41
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 41
[08-04 10:43:19.423] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.423] [26468 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.423] [26468 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.439] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 41
[08-04 10:43:19.439] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.439] [26670 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6059a30 len 0x4
[08-04 10:43:19.439] [26670 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.439] [26670 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.439] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 41
[08-04 10:43:19.439] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.439] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.439] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.439] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.439] [26670 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.439] [26670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 42
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x34 len 0x4
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x34 len 0x4
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 42
[08-04 10:43:19.442] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.442] [26700 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.442] [26700 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.458] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 42
[08-04 10:43:19.458] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.458] [26902 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc605b730 len 0x4
[08-04 10:43:19.458] [26902 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.458] [26902 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.458] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 42
[08-04 10:43:19.458] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.458] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.458] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.458] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.458] [26902 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.458] [26902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 43
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 43
[08-04 10:43:19.461] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.461] [26932 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.461] [26932 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.477] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 43
[08-04 10:43:19.477] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.477] [27134 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc605d430 len 0x4
[08-04 10:43:19.477] [27134 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.477] [27134 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.477] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 43
[08-04 10:43:19.477] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.477] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.477] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.477] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.477] [27134 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.477] [27134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 44
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 44
[08-04 10:43:19.479] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.480] [27164 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.480] [27164 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.496] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 44
[08-04 10:43:19.496] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.496] [27366 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc605f130 len 0x4
[08-04 10:43:19.496] [27366 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.496] [27366 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.496] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 44
[08-04 10:43:19.496] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.496] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.496] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.496] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.496] [27366 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.496] [27366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 45
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 45
[08-04 10:43:19.498] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.498] [27396 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.498] [27396 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.514] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 45
[08-04 10:43:19.514] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.514] [27598 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6060e30 len 0x4
[08-04 10:43:19.514] [27598 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.514] [27598 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.514] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 45
[08-04 10:43:19.514] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.514] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.514] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.514] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.514] [27598 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.514] [27598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.516] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.516] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.517] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.517] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.517] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 46
[08-04 10:43:19.517] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.517] [27626 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-04 10:43:19.517] [27626 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.517] [27626 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-04 10:43:19.517] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 46
[08-04 10:43:19.517] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.517] [27628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.517] [27628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.546] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 46
[08-04 10:43:19.546] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.546] [27830 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc41723d0 len 0x4
[08-04 10:43:19.546] [27830 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.546] [27830 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.546] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 46
[08-04 10:43:19.546] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.546] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.546] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.546] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.546] [27830 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.546] [27830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 47
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 47
[08-04 10:43:19.549] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.549] [27860 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.549] [27860 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.563] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 47
[08-04 10:43:19.563] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.563] [28036 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc63c6640 len 0x4
[08-04 10:43:19.563] [28036 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.563] [28036 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.563] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 47
[08-04 10:43:19.563] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.563] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.563] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.563] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.563] [28036 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.563] [28036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 48
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 48
[08-04 10:43:19.565] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.566] [28066 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.566] [28066 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:19.572] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 48
[08-04 10:43:19.572] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.572] [28142 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc615b960 len 0x4
[08-04 10:43:19.572] [28142 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.572] [28142 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.572] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 48
[08-04 10:43:19.572] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.572] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.572] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.572] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.572] [28142 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.572] [28142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 49
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 49
[08-04 10:43:19.574] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.574] [28172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.574] [28172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:19.996] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 49
[08-04 10:43:19.996] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:19.996] [33506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6023f90 len 0x4
[08-04 10:43:19.996] [33506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.996] [33506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:19.996] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 49
[08-04 10:43:19.996] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:19.996] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.996] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.996] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.996] [33506 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:19.996] [33506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 50
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 50
[08-04 10:43:19.999] [33534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:19.999] [33536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:19.999] [33536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.015] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 50
[08-04 10:43:20.015] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.015] [33738 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6062990 len 0x4
[08-04 10:43:20.015] [33738 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.015] [33738 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.015] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 50
[08-04 10:43:20.015] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.015] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.015] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.015] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.015] [33738 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.015] [33738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 51
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 51
[08-04 10:43:20.017] [33766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.017] [33768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.017] [33768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:20.033] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 51
[08-04 10:43:20.033] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.033] [33970 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6064690 len 0x4
[08-04 10:43:20.033] [33970 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.033] [33970 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.033] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 51
[08-04 10:43:20.033] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.033] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.033] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.033] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.034] [33970 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.034] [33970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 52
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x77 len 0x4
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x77 len 0x4
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 52
[08-04 10:43:20.036] [33998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.036] [34 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.036] [34 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.052] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 52
[08-04 10:43:20.052] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.052] [34202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6066390 len 0x4
[08-04 10:43:20.052] [34202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.052] [34202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.052] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 52
[08-04 10:43:20.052] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.052] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.052] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.052] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.052] [34202 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.052] [34202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 53
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 53
[08-04 10:43:20.054] [34230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.054] [34232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.054] [34232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:20.070] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 53
[08-04 10:43:20.070] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.070] [34434 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6068090 len 0x4
[08-04 10:43:20.070] [34434 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.070] [34434 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.070] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 53
[08-04 10:43:20.070] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.070] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.070] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.070] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.070] [34434 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.070] [34434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 54
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 54
[08-04 10:43:20.072] [34462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.072] [34464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.072] [34464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.088] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 54
[08-04 10:43:20.088] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.088] [34666 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc6069d90 len 0x4
[08-04 10:43:20.088] [34666 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.088] [34666 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.088] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 54
[08-04 10:43:20.088] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.088] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.088] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.088] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.088] [34666 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.088] [34666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 55
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 55
[08-04 10:43:20.090] [34694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.091] [34696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.091] [34696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:20.106] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 55
[08-04 10:43:20.106] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.106] [34898 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc606ba90 len 0x4
[08-04 10:43:20.106] [34898 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.106] [34898 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.106] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 55
[08-04 10:43:20.106] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.106] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.106] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.106] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.106] [34898 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.106] [34898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 56
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61 len 0x4
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x61 len 0x4
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 56
[08-04 10:43:20.108] [34926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.109] [34928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.109] [34928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.124] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 56
[08-04 10:43:20.124] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.124] [35130 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5fe2aa0 len 0x4
[08-04 10:43:20.124] [35130 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.124] [35130 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.124] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 56
[08-04 10:43:20.124] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.124] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.124] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.124] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.124] [35130 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.124] [35130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 57
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 57
[08-04 10:43:20.126] [35158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.127] [35160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.127] [35160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:20.142] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 57
[08-04 10:43:20.142] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.142] [35362 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5fe47a0 len 0x4
[08-04 10:43:20.142] [35362 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.142] [35362 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.142] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 57
[08-04 10:43:20.142] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.142] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.142] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.142] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.142] [35362 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.142] [35362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 58
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 58
[08-04 10:43:20.145] [35390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.145] [35392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.145] [35392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.161] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 58
[08-04 10:43:20.161] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.161] [35594 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5fe64a0 len 0x4
[08-04 10:43:20.161] [35594 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.161] [35594 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.161] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 58
[08-04 10:43:20.161] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.161] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.161] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.161] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.161] [35594 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.161] [35594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 59
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 59
[08-04 10:43:20.163] [35622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.163] [35624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.163] [35624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:20.179] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 59
[08-04 10:43:20.179] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.179] [35826 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5fe81a0 len 0x4
[08-04 10:43:20.179] [35826 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.179] [35826 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.179] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 59
[08-04 10:43:20.179] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.179] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.179] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.179] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.179] [35826 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.179] [35826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 60
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 60
[08-04 10:43:20.181] [35854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.181] [35856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.181] [35856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.197] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 60
[08-04 10:43:20.197] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.197] [36058 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5fe9e80 len 0x4
[08-04 10:43:20.197] [36058 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.197] [36058 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.197] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 60
[08-04 10:43:20.197] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.197] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.197] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.197] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.197] [36058 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.197] [36058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 61
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 61
[08-04 10:43:20.200] [36086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.200] [36088 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.200] [36088 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:20.216] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 61
[08-04 10:43:20.216] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.216] [36290 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5febb80 len 0x4
[08-04 10:43:20.216] [36290 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.216] [36290 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.216] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 61
[08-04 10:43:20.216] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.216] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.216] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.216] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.216] [36290 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.216] [36290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 62
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 62
[08-04 10:43:20.218] [36318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.218] [36320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.218] [36320 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.235] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 62
[08-04 10:43:20.235] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.235] [36522 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5fed880 len 0x4
[08-04 10:43:20.235] [36522 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.235] [36522 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.235] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 62
[08-04 10:43:20.235] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.235] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.235] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.235] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.235] [36522 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.235] [36522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 63
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 63
[08-04 10:43:20.237] [36550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.237] [36552 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.237] [36552 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:20.254] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 63
[08-04 10:43:20.254] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.254] [36754 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5fefeb0 len 0x4
[08-04 10:43:20.254] [36754 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.254] [36754 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.254] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 63
[08-04 10:43:20.254] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.254] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.254] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.254] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.254] [36754 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.254] [36754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 64
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 64
[08-04 10:43:20.256] [36782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.256] [36784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.256] [36784 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.273] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 64
[08-04 10:43:20.273] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.273] [36986 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5ff1bb0 len 0x4
[08-04 10:43:20.273] [36986 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.273] [36986 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.273] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 64
[08-04 10:43:20.273] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.273] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.273] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.273] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.273] [36986 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.273] [36986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 65
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49 len 0x4
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x49 len 0x4
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 65
[08-04 10:43:20.275] [37014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.275] [37016 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.275] [37016 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:20.292] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 65
[08-04 10:43:20.292] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.292] [37218 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5ff38b0 len 0x4
[08-04 10:43:20.292] [37218 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.292] [37218 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.292] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 65
[08-04 10:43:20.292] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.292] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.292] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.292] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.292] [37218 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.292] [37218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 66
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4c len 0x4
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4c len 0x4
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 66
[08-04 10:43:20.294] [37246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.294] [37248 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.294] [37248 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.311] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 66
[08-04 10:43:20.311] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.311] [37450 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5ff55b0 len 0x4
[08-04 10:43:20.311] [37450 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.311] [37450 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.311] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 66
[08-04 10:43:20.311] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.311] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.311] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.311] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.311] [37450 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.311] [37450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 67
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 67
[08-04 10:43:20.313] [37478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.313] [37480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.313] [37480 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
[08-04 10:43:20.330] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c60c4ea0, reqid: 67
[08-04 10:43:20.330] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.330] [37682 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5ff72b0 len 0x4
[08-04 10:43:20.330] [37682 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.330] [37682 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.330] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 67
[08-04 10:43:20.330] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.330] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.330] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.330] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.330] [37682 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.330] [37682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 68
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 68
[08-04 10:43:20.332] [37710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.332] [37712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.332] [37712 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.347] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 68
[08-04 10:43:20.347] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.347] [37888 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5ff8c70 len 0x4
[08-04 10:43:20.347] [37888 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.347] [37888 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.347] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 68
[08-04 10:43:20.347] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.347] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.347] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.347] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.347] [37888 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.347] [37888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c41758b0, reqid: 69
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c60c4ea0
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c41758b0, wt: 0x7f16c60c4ea0
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 69
[08-04 10:43:20.349] [37916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.349] [37918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.349] [37918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c41758b0
[08-04 10:43:20.355] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f16c41758b0, reqid: 69
[08-04 10:43:20.355] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-04 10:43:20.356] [37994 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xc5ff99b0 len 0x4
[08-04 10:43:20.356] [37994 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.356] [37994 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-04 10:43:20.356] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 69
[08-04 10:43:20.356] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-04 10:43:20.356] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.356] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.356] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.356] [37994 ns] axi2tlm: rdata.write 0x0
[08-04 10:43:20.356] [37994 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f16c41758b0
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f16c60c4ea0, reqid: 70
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f16c60c4ea0, wt: 0x7f16c41758b0
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 70
[08-04 10:43:20.358] [38022 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-04 10:43:20.358] [38024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-04 10:43:20.358] [38024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f16c60c4ea0
