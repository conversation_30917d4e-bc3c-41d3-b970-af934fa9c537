[08-04 13:45:17.983] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-04 13:45:22.997] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:22.997] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:22.998] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30173
[08-04 13:45:22.998] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:22.998] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:45:22.998] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:22.998] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.000] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.000] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.001] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30184
[08-04 13:45:23.001] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.001] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:45:23.001] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.001] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.003] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.003] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.004] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30195
[08-04 13:45:23.004] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.004] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:45:23.004] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.004] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.006] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.006] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.006] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30206
[08-04 13:45:23.006] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.006] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:45:23.006] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.006] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.008] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.008] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.009] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30217
[08-04 13:45:23.009] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.009] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:45:23.009] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.009] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.011] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.011] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.011] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30228
[08-04 13:45:23.011] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.011] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:45:23.011] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.011] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.040] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.040] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.040] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30402
[08-04 13:45:23.040] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.040] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:45:23.040] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.040] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.042] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.042] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.043] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 30415
[08-04 13:45:23.043] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.043] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:45:23.043] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.043] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.070] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.070] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.072] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30578
[08-04 13:45:23.072] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.072] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:45:23.072] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.072] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.075] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.075] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.075] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 30591
[08-04 13:45:23.076] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.076] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:45:23.076] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.076] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.095] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.095] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.097] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 30706
[08-04 13:45:23.097] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.097] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30707, check_time is: 61414000
[08-04 13:45:23.097] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.097] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[08-04 13:45:23.097] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-04 13:45:23.097] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61412000
[08-04 13:45:23.098] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61414000
[08-04 13:45:23.098] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61414000
[08-04 13:45:23.098] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:45:23.098] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31090, check_time is: 62180000
[08-04 13:45:23.098] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:45:23.098] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-04 13:45:23.098] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61414000
[08-04 13:45:23.100] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:45:23.100] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:45:23.100] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 30717
[08-04 13:45:23.110] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 0
[08-04 13:45:23.110] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:45:23.177] [62180 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62180000
[08-04 13:45:23.177] [62180 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62180000
