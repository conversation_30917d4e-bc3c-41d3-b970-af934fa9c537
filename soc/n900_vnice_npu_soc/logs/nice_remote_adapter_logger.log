[08-04 13:30:02.770] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-04 13:30:07.542] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.542] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.543] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30173
[08-04 13:30:07.543] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.543] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.543] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.543] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.545] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.545] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.546] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30184
[08-04 13:30:07.546] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.546] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.546] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.546] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.548] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.548] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.549] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30195
[08-04 13:30:07.549] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.549] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.549] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.549] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.551] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.551] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.551] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30206
[08-04 13:30:07.551] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.551] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.551] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.551] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.553] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.553] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.553] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30217
[08-04 13:30:07.554] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.554] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.554] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.554] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.555] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.555] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.556] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30228
[08-04 13:30:07.556] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.556] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.556] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.556] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.583] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.583] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.583] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30402
[08-04 13:30:07.583] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.583] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.583] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.583] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.585] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.585] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.586] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 30415
[08-04 13:30:07.586] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.586] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.586] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.586] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.611] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.611] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.612] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30578
[08-04 13:30:07.612] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.612] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.612] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.612] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.614] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.614] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.615] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 30591
[08-04 13:30:07.616] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.616] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.616] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.616] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.634] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.634] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.636] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 30706
[08-04 13:30:07.636] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.636] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30707, check_time is: 61414000
[08-04 13:30:07.636] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.636] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[08-04 13:30:07.636] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-04 13:30:07.636] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61412000
[08-04 13:30:07.637] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61414000
[08-04 13:30:07.637] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61414000
[08-04 13:30:07.637] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.637] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31218, check_time is: 62436000
[08-04 13:30:07.637] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.637] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-04 13:30:07.637] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61414000
[08-04 13:30:07.639] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:30:07.639] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:30:07.639] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 30717
[08-04 13:30:07.648] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:30:07.648] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:30:07.648] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:30:07.648] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:30:07.649] [61436 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 61436000
