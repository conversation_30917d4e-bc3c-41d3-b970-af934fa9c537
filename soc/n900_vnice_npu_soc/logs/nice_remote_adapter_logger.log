[08-04 09:55:23.873] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-04 09:55:27.212] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.212] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.213] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30173
[08-04 09:55:27.213] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.213] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.213] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.213] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.215] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.215] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.216] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30184
[08-04 09:55:27.216] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.216] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.216] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.216] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.218] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.218] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.219] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30195
[08-04 09:55:27.219] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.219] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.219] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.219] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.221] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.221] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.222] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30206
[08-04 09:55:27.222] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.222] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.222] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.222] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.223] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.223] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.224] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30217
[08-04 09:55:27.224] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.224] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.224] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.224] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.226] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.226] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.226] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30228
[08-04 09:55:27.226] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.226] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.226] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.226] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.259] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.259] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.259] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30402
[08-04 09:55:27.259] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.259] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.259] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.259] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.262] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.262] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.262] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 30415
[08-04 09:55:27.262] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.262] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.262] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.262] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.290] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.290] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.291] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30578
[08-04 09:55:27.291] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.291] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.291] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.291] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.293] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.293] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.294] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 30591
[08-04 09:55:27.295] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.295] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.295] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.295] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.314] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.314] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.315] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 30706
[08-04 09:55:27.315] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.315] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30706, check_time is: 61412000
[08-04 09:55:27.315] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.315] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[08-04 09:55:27.315] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-04 09:55:27.315] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61412000
[08-04 09:55:27.315] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61412000
[08-04 09:55:27.315] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61412000
[08-04 09:55:27.316] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.316] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 1410096114, check_time is: 2820192228000
[08-04 09:55:27.316] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.317] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-04 09:55:27.317] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61412000
[08-04 09:55:27.319] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 09:55:27.319] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 09:55:27.319] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 30717
[08-04 09:55:27.320] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 09:55:27.320] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 09:55:27.320] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 09:55:27.320] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 09:55:27.320] [61436 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 61436000
