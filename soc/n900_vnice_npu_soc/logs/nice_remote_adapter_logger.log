[08-04 13:39:15.616] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-04 13:39:18.665] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.665] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.666] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30173
[08-04 13:39:18.666] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.666] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:39:18.666] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.666] [60346 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.667] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.667] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.669] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30184
[08-04 13:39:18.669] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.669] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:39:18.669] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.669] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.670] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.670] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.671] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30195
[08-04 13:39:18.671] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.671] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:39:18.671] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.671] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.672] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.672] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.673] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30206
[08-04 13:39:18.673] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.673] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:39:18.673] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.673] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.674] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.674] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.675] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30217
[08-04 13:39:18.675] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.675] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:39:18.675] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.675] [60434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.676] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.676] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.677] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30228
[08-04 13:39:18.677] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.677] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:39:18.677] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.677] [60456 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.696] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.696] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.697] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30402
[08-04 13:39:18.697] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.697] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:39:18.697] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.697] [60804 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.698] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.698] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.699] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 30415
[08-04 13:39:18.699] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.699] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:39:18.699] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.699] [60830 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.716] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.716] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.717] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30578
[08-04 13:39:18.717] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.717] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:39:18.717] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.717] [61156 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.719] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.719] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.720] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 30591
[08-04 13:39:18.720] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.720] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-04 13:39:18.720] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.720] [61182 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.733] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.733] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.735] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 30706
[08-04 13:39:18.735] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.735] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30707, check_time is: 61414000
[08-04 13:39:18.735] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.735] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[08-04 13:39:18.735] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-04 13:39:18.735] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61412000
[08-04 13:39:18.736] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61414000
[08-04 13:39:18.736] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61414000
[08-04 13:39:18.736] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-04 13:39:18.736] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31218, check_time is: 62436000
[08-04 13:39:18.736] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-04 13:39:18.736] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-04 13:39:18.736] [61414 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61414000
[08-04 13:39:18.737] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-04 13:39:18.737] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-04 13:39:18.738] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 30717
[08-04 13:39:18.748] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 0
[08-04 13:39:18.748] [61434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-04 13:39:18.812] [62436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62436000
[08-04 13:39:18.812] [62436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62436000
