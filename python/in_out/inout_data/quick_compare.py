#!/usr/bin/env python3
"""
快速对比输入输出数据脚本
简化版本，用于快速检查数据是否一致
"""

import torch
import os
from pathlib import Path


def quick_compare():
    """快速对比输入输出数据"""
    
    # 文件路径
    in_file = "in_data/in1_0_0.pt"
    out_file = "out_data/out1_0_1.pt"
    
    print("🔍 快速对比输入输出数据...")
    print(f"输入文件: {in_file}")
    print(f"输出文件: {out_file}")
    
    # 检查文件是否存在
    if not os.path.exists(in_file):
        print(f"❌ 输入文件不存在: {in_file}")
        return False
    
    if not os.path.exists(out_file):
        print(f"❌ 输出文件不存在: {out_file}")
        return False
    
    try:
        # 加载tensor
        in_tensor = torch.load(in_file, map_location='cpu')
        out_tensor = torch.load(out_file, map_location='cpu')
        
        print(f"\n📊 基本信息:")
        print(f"输入形状: {in_tensor.shape}, 数据类型: {in_tensor.dtype}")
        print(f"输出形状: {out_tensor.shape}, 数据类型: {out_tensor.dtype}")
        
        # 形状检查
        if in_tensor.shape != out_tensor.shape:
            print("❌ 形状不匹配")
            return False
        
        # 数据类型检查
        if in_tensor.dtype != out_tensor.dtype:
            print("⚠️  数据类型不匹配")
        
        # 统计信息
        print(f"\n📈 数据统计:")
        print(f"输入 - min: {in_tensor.min():.6f}, max: {in_tensor.max():.6f}, mean: {in_tensor.mean():.6f}")
        print(f"输出 - min: {out_tensor.min():.6f}, max: {out_tensor.max():.6f}, mean: {out_tensor.mean():.6f}")
        
        # 快速判断
        is_equal = torch.equal(in_tensor, out_tensor)
        
        print(f"\n🎯 对比结果:")
        if is_equal:
            print("✅ 输入输出数据完全一致")
            return True
        else:
            # 检查是否为全零
            is_input_zeros = torch.allclose(in_tensor, torch.zeros_like(in_tensor))
            is_output_zeros = torch.allclose(out_tensor, torch.zeros_like(out_tensor))
            
            print("❌ 输入输出数据不一致")
            print(f"输入是否为全零: {'是' if is_input_zeros else '否'}")
            print(f"输出是否为全零: {'是' if is_output_zeros else '否'}")
            
            # 差异统计
            diff = torch.abs(in_tensor - out_tensor)
            max_diff = torch.max(diff).item()
            nonzero_diff = torch.count_nonzero(diff).item()
            total_elements = in_tensor.numel()
            diff_percent = (nonzero_diff / total_elements) * 100
            
            print(f"最大差异: {max_diff:.6f}")
            print(f"不同元素: {nonzero_diff}/{total_elements} ({diff_percent:.1f}%)")
            
            return False
        
    except Exception as e:
        print(f"❌ 加载或对比过程中出错: {e}")
        return False


if __name__ == "__main__":
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 执行快速对比
    result = quick_compare()
    
    print(f"\n{'='*40}")
    if result:
        print("✅ 结论: 数据一致")
    else:
        print("❌ 结论: 数据不一致")
    print(f"{'='*40}")