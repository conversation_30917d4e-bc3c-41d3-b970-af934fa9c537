#!/usr/bin/env python3
"""
输入输出数据对比脚本
对比 in_data/ 和 out_data/ 目录中的 PyTorch tensor 文件
"""

import torch
import numpy as np
import os
import json
from pathlib import Path
import argparse


def load_tensor_configs():
    """加载tensor配置信息"""
    input_config_path = "input_tensor.json"
    output_config_path = "output_tensor.json"
    
    configs = {}
    
    if os.path.exists(input_config_path):
        with open(input_config_path, 'r') as f:
            configs['input'] = json.load(f)
    
    if os.path.exists(output_config_path):
        with open(output_config_path, 'r') as f:
            configs['output'] = json.load(f)
    
    return configs


def load_pytorch_tensor(file_path):
    """加载PyTorch tensor文件"""
    try:
        tensor = torch.load(file_path, map_location='cpu')
        return tensor
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None


def compare_tensors(tensor1, tensor2, name1="Input", name2="Output", tolerance=1e-6):
    """
    详细对比两个tensor
    
    Args:
        tensor1: 第一个tensor
        tensor2: 第二个tensor
        name1: 第一个tensor的名称
        name2: 第二个tensor的名称
        tolerance: 数值比较的容忍度
    
    Returns:
        bool: 是否相等
    """
    print(f"\n=== 对比 {name1} vs {name2} ===")
    
    # 检查是否都成功加载
    if tensor1 is None or tensor2 is None:
        print("❌ 其中一个或两个tensor加载失败")
        return False
    
    # 检查数据类型
    print(f"数据类型: {name1}={tensor1.dtype}, {name2}={tensor2.dtype}")
    if tensor1.dtype != tensor2.dtype:
        print("⚠️  数据类型不匹配")
    
    # 检查形状
    print(f"形状: {name1}={tensor1.shape}, {name2}={tensor2.shape}")
    if tensor1.shape != tensor2.shape:
        print("❌ 形状不匹配")
        return False
    
    # 检查设备
    print(f"设备: {name1}={tensor1.device}, {name2}={tensor2.device}")
    
    # 统计信息
    print(f"\n统计信息:")
    print(f"  {name1}: min={tensor1.min():.6f}, max={tensor1.max():.6f}, mean={tensor1.mean():.6f}")
    print(f"  {name2}: min={tensor2.min():.6f}, max={tensor2.max():.6f}, mean={tensor2.mean():.6f}")
    
    # 精确相等检查
    exact_equal = torch.equal(tensor1, tensor2)
    print(f"\n精确相等: {'✅' if exact_equal else '❌'}")
    
    if exact_equal:
        print("✅ 两个tensor完全相等！")
        return True
    
    # 近似相等检查
    try:
        close_equal = torch.allclose(tensor1, tensor2, rtol=tolerance, atol=tolerance)
        print(f"近似相等 (tolerance={tolerance}): {'✅' if close_equal else '❌'}")
        
        # 计算差异
        diff = torch.abs(tensor1 - tensor2)
        max_diff = torch.max(diff).item()
        mean_diff = torch.mean(diff).item()
        
        print(f"\n差异分析:")
        print(f"  最大差异: {max_diff:.8f}")
        print(f"  平均差异: {mean_diff:.8f}")
        print(f"  非零差异元素数量: {torch.count_nonzero(diff).item()}")
        print(f"  总元素数量: {tensor1.numel()}")
        
        # 差异百分比
        diff_percentage = (torch.count_nonzero(diff).item() / tensor1.numel()) * 100
        print(f"  差异元素百分比: {diff_percentage:.2f}%")
        
        return close_equal
        
    except Exception as e:
        print(f"❌ 比较过程中出错: {e}")
        return False


def compare_directories(in_dir="in_data", out_dir="out_data", tolerance=1e-6):
    """
    对比两个目录中的所有tensor文件
    
    Args:
        in_dir: 输入数据目录
        out_dir: 输出数据目录
        tolerance: 数值比较的容忍度
    """
    print("🔍 开始对比输入输出数据...")
    
    # 加载配置
    configs = load_tensor_configs()
    
    # 获取文件列表
    in_path = Path(in_dir)
    out_path = Path(out_dir)
    
    if not in_path.exists():
        print(f"❌ 输入目录不存在: {in_dir}")
        return False
    
    if not out_path.exists():
        print(f"❌ 输出目录不存在: {out_dir}")
        return False
    
    # 获取.pt文件
    in_files = list(in_path.glob("*.pt"))
    out_files = list(out_path.glob("*.pt"))
    
    print(f"\n📁 文件统计:")
    print(f"  输入文件数量: {len(in_files)}")
    print(f"  输出文件数量: {len(out_files)}")
    
    if len(in_files) == 0 and len(out_files) == 0:
        print("⚠️  两个目录都没有.pt文件")
        return True
    
    all_equal = True
    
    # 如果只有一个输入文件和一个输出文件，直接对比
    if len(in_files) == 1 and len(out_files) == 1:
        in_file = in_files[0]
        out_file = out_files[0]
        
        print(f"\n📄 对比文件:")
        print(f"  输入: {in_file}")
        print(f"  输出: {out_file}")
        
        # 加载tensor
        in_tensor = load_pytorch_tensor(in_file)
        out_tensor = load_pytorch_tensor(out_file)
        
        # 对比
        is_equal = compare_tensors(in_tensor, out_tensor, 
                                 in_file.stem, out_file.stem, tolerance)
        all_equal = all_equal and is_equal
    
    else:
        # 按文件名对应关系对比
        in_files_dict = {f.stem: f for f in in_files}
        out_files_dict = {f.stem: f for f in out_files}
        
        # 找到匹配的文件对
        common_names = set(in_files_dict.keys()) & set(out_files_dict.keys())
        
        if not common_names:
            print("⚠️  没有找到匹配的文件名")
            # 尝试按索引对比
            for i, (in_file, out_file) in enumerate(zip(in_files, out_files)):
                print(f"\n📄 对比第{i+1}对文件:")
                print(f"  输入: {in_file}")
                print(f"  输出: {out_file}")
                
                in_tensor = load_pytorch_tensor(in_file)
                out_tensor = load_pytorch_tensor(out_file)
                
                is_equal = compare_tensors(in_tensor, out_tensor, 
                                         in_file.stem, out_file.stem, tolerance)
                all_equal = all_equal and is_equal
        else:
            # 按匹配的文件名对比
            for name in common_names:
                in_file = in_files_dict[name]
                out_file = out_files_dict[name]
                
                print(f"\n📄 对比文件: {name}")
                print(f"  输入: {in_file}")
                print(f"  输出: {out_file}")
                
                in_tensor = load_pytorch_tensor(in_file)
                out_tensor = load_pytorch_tensor(out_file)
                
                is_equal = compare_tensors(in_tensor, out_tensor, 
                                         f"Input_{name}", f"Output_{name}", tolerance)
                all_equal = all_equal and is_equal
    
    # 总结
    print(f"\n{'='*50}")
    print(f"📊 对比结果总结:")
    if all_equal:
        print("✅ 所有对比的tensor都相等！")
    else:
        print("❌ 存在不相等的tensor")
    print(f"{'='*50}")
    
    return all_equal


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='对比输入输出tensor数据')
    parser.add_argument('--in_dir', default='in_data', help='输入数据目录')
    parser.add_argument('--out_dir', default='out_data', help='输出数据目录')
    parser.add_argument('--tolerance', type=float, default=1e-6, 
                       help='数值比较的容忍度')
    parser.add_argument('--verbose', action='store_true', 
                       help='显示详细信息')
    
    args = parser.parse_args()
    
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 执行对比
    result = compare_directories(args.in_dir, args.out_dir, args.tolerance)
    
    # 返回退出码
    exit(0 if result else 1)


if __name__ == "__main__":
    main()