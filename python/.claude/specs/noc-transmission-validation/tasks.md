# NoC Transmission Implementation Tasks

## Overview
Break down the NoC transmission implementation into atomic, agent-friendly tasks.

## Task Breakdown

### Phase 1: Primitive Type Separation

- [x] **Task 1.1**: Activate NOC_SRC and NOC_DEST primitive types in SIMTemplate.py
  - **Requirements**: REQ-IP-001
  - **Files**: SIMTemplate.py
  - **Description**: Remove "Fake" comment from NOC_SRC, NOC_DEST, NOC_FENCE enum values
  - **Leverage**: Existing enum structure already has these types defined

- [x] **Task 1.2**: Update instruction-to-primitive mapping in tensorinfo.py
  - **Requirements**: REQ-IP-001
  - **Files**: tensorinfo.py
  - **Description**: Change mapping from PrimName.NOC to PrimName.NOC_SRC/NOC_DEST/NOC_FENCE
  - **Leverage**: Existing convert_PrimName dictionary structure

- [x] **Task 1.3**: Add NOC to deprecated primitives with NotImplementedError
  - **Requirements**: REQ-IP-001
  - **Files**: SIMTemplate.py
  - **Description**: Update golden_run case for PrimName.NOC to raise NotImplementedError
  - **Leverage**: Existing golden_run switch statement

### Phase 2: SIMTop Pairing Mechanism

- [x] **Task 2.1**: Add NoC primitive tracking fields to PNMDie class
  - **Requirements**: REQ-IP-004, REQ-SY-001
  - **Files**: SIMTop.py
  - **Description**: Add pending_noc_primitives list and noc_timeout_cycles to __init__
  - **Leverage**: Existing PNMDie class structure

- [x] **Task 2.2**: Modify primitive dispatch logic in run_simulation_until
  - **Requirements**: REQ-IP-004, REQ-SY-001
  - **Files**: SIMTop.py
  - **Description**: Add NoC primitive handling branch after SYNC check
  - **Leverage**: Existing if-elif structure for primitive types

- [x] **Task 2.3**: Implement handle_noc_primitive method in PNMDie
  - **Requirements**: REQ-IP-004, REQ-SY-002
  - **Files**: SIMTop.py
  - **Description**: Create method to find matching pairs regardless of arrival order (SRC first or DEST first), immediately dispatch paired primitives when both are available
  - **Implementation**: Check pending list for partner, if found execute transfer, if not found add to pending
  - **Leverage**: None - new method

- [x] **Task 2.4**: Implement is_matching_pair method in PNMDie
  - **Requirements**: REQ-SY-002
  - **Files**: SIMTop.py
  - **Description**: Create method to check if two primitives form valid NoC pair
  - **Leverage**: None - new method

- [x] **Task 2.5**: Implement execute_noc_transfer_pair method in PNMDie
  - **Requirements**: REQ-SY-002, REQ-DT-001
  - **Files**: SIMTop.py
  - **Description**: Create method to link paired primitives and simultaneously dispatch to both source and destination cores, no execution order dependency
  - **Implementation**: Link primitives via partner_primitive field, dispatch SRC to source core, dispatch DEST to destination core
  - **Leverage**: Existing prim_receive method

- [x] **Task 2.6**: Implement check_noc_timeouts method in PNMDie (Optional)
  - **Requirements**: REQ-SY-003
  - **Files**: SIMTop.py
  - **Description**: Create method to detect and report unpaired primitive timeouts
  - **Note**: Optional enhancement - not required for MVP
  - **Leverage**: None - new method

- [x] **Task 2.7**: Add timeout check call in run_simulation_until (Optional)
  - **Requirements**: REQ-SY-003
  - **Files**: SIMTop.py
  - **Description**: Call check_noc_timeouts every 100 cycles
  - **Note**: Optional enhancement - not required for MVP
  - **Leverage**: Existing cycle counter

### Phase 3: NoC Settings Enhancement

- [x] **Task 3.1**: Add new fields to NoCInfo class
  - **Requirements**: REQ-DT-001, REQ-DT-004
  - **Files**: SIMTemplate.py
  - **Description**: Add transfer_size, data_buffer, partner_primitive, route_established fields
  - **Leverage**: Existing NoCInfo class structure

- [x] **Task 3.2**: Add dispatch_cycle field to Primitive class
  - **Requirements**: REQ-SY-003
  - **Files**: SIMTemplate.py
  - **Description**: Add field to track when primitive was dispatched
  - **Leverage**: Existing Primitive class

### Phase 4: Resource Allocation Fix

- [x] **Task 4.1**: Define separate primitive type lists in prim_module_use
  - **Requirements**: REQ-IP-002, REQ-IP-003
  - **Files**: NPUCore.py
  - **Description**: Create noc_src_list, noc_dest_list, noc_fence_list variables
  - **Leverage**: Existing list pattern for other primitive types

- [x] **Task 4.2**: Implement NOC_SRC resource allocation logic
  - **Requirements**: REQ-IP-002
  - **Files**: NPUCore.py
  - **Description**: Add elif branch for NOC_SRC that only allocates TX router and source memory
  - **Leverage**: Existing resource allocation pattern

- [x] **Task 4.3**: Implement NOC_DEST resource allocation logic
  - **Requirements**: REQ-IP-003
  - **Files**: NPUCore.py
  - **Description**: Add elif branch for NOC_DEST that only allocates RX router and dest memory
  - **Leverage**: Existing resource allocation pattern

- [x] **Task 4.4**: Implement NOC_FENCE resource allocation logic
  - **Requirements**: REQ-IP-004
  - **Files**: NPUCore.py
  - **Description**: Add elif branch for NOC_FENCE with no resource allocation
  - **Leverage**: Existing elif structure

- [x] **Task 4.5**: Add core matching logic for resource allocation
  - **Requirements**: REQ-IP-002, REQ-IP-003
  - **Files**: NPUCore.py
  - **Description**: Add logic to check if current core matches primitive's src/dst before allocating resources
  - **Implementation**: For NOC_SRC: only allocate if (self.group == prim.noc_settings.src_group and self.id == prim.noc_settings.src_id); For NOC_DEST: only allocate if (self.group == prim.noc_settings.dst_group and self.id == prim.noc_settings.dst_id)
  - **Leverage**: Existing group/id matching patterns in prim_exe

### Phase 5: TensorInfo Handlers

- [x] **Task 5.1**: Update fill_TensorInfo to handle separate NoC types
  - **Requirements**: REQ-DT-001
  - **Files**: tensorinfo.py
  - **Description**: Add NOC_SRC, NOC_DEST, NOC_FENCE to primitive type groups
  - **Leverage**: Existing fill_TensorInfo pattern

- [x] **Task 5.2**: Implement fill_TensorInfo_noc_src function
  - **Requirements**: REQ-MA-001, REQ-DT-003
  - **Files**: tensorinfo.py
  - **Description**: Create function to set up source tensor info and calculate transfer size
  - **Leverage**: Existing fill_TensorInfo patterns for other types

- [x] **Task 5.3**: Implement fill_TensorInfo_noc_dest function
  - **Requirements**: REQ-MA-002, REQ-DT-003
  - **Files**: tensorinfo.py
  - **Description**: Create function to set up destination tensor info
  - **Leverage**: Existing fill_TensorInfo patterns

### Phase 6: NoC Primitive Execution

- [x] **Task 6.1**: Add NOC_SRC execution logic in prim_exe
  - **Requirements**: REQ-DT-001, REQ-MA-001
  - **Files**: NPUCore.py
  - **Description**: Add if branch for NOC_SRC that reads source data and stores in data_buffer, executes independently without waiting for DEST
  - **Implementation**: Check if current core is source, read memory data, store in noc_settings.data_buffer, execute TX router
  - **Leverage**: Existing prim_exe structure

- [x] **Task 6.2**: Add NOC_DEST execution logic in prim_exe
  - **Requirements**: REQ-DT-001, REQ-MA-002
  - **Files**: NPUCore.py
  - **Description**: Add elif branch for NOC_DEST that retrieves data from partner primitive and writes to destination, executes independently without waiting for SRC
  - **Implementation**: Check if current core is destination, get data from partner_primitive.noc_settings.data_buffer, write to memory
  - **Leverage**: Existing prim_exe structure

- [x] **Task 6.3**: Implement execute_noc_routing method (Optional - not needed for current architecture)
  - **Requirements**: REQ-DT-002, REQ-AC-001
  - **Files**: NPUCore.py
  - **Description**: Create method to establish NoC route using existing NoC.py functions
  - **Leverage**: Existing NoC routing_request and routing methods

- [x] **Task 6.4**: Implement complete_noc_transfer method (Optional - not needed for current architecture)
  - **Requirements**: REQ-DT-002, REQ-SY-004
  - **Files**: NPUCore.py
  - **Description**: Create method to free NoC route after transfer
  - **Leverage**: Existing NoC routing_free method

- [x] **Task 6.5**: Implement read_memory_data method (Optional - handled by golden model)
  - **Requirements**: REQ-MA-001, REQ-MA-003
  - **Files**: NPUCore.py
  - **Description**: Create method to read data from memory module as numpy array
  - **Leverage**: Existing memory buffer access patterns

- [x] **Task 6.6**: Implement write_memory_data method (Optional - handled by golden model)
  - **Requirements**: REQ-MA-002, REQ-MA-003
  - **Files**: NPUCore.py
  - **Description**: Create method to write numpy array data to memory module
  - **Leverage**: Existing memory buffer access patterns

- [x] **Task 6.7**: Implement data synchronization between paired primitives (Implemented through pairing mechanism)
  - **Requirements**: REQ-DT-001, REQ-SY-002
  - **Files**: NPUCore.py
  - **Description**: Ensure data transfer works regardless of SRC/DEST execution order using shared data_buffer
  - **Implementation**: If SRC executes first: store data in data_buffer for DEST to retrieve; If DEST executes first: wait for partner's data_buffer to be populated; Use partner_primitive link to access shared data
  - **Leverage**: Existing primitive execution framework

### Phase 7: Golden Model Implementation

- [x] **Task 7.1**: Implement NOC_SRC case in golden_run
  - **Requirements**: REQ-GM-001, REQ-GM-003
  - **Files**: SIMTemplate.py
  - **Description**: Add case for NOC_SRC that reads tensor and stores in data_buffer
  - **Leverage**: Existing golden_run tensor operations

- [x] **Task 7.2**: Implement NOC_DEST case in golden_run
  - **Requirements**: REQ-GM-001, REQ-GM-002
  - **Files**: SIMTemplate.py
  - **Description**: Add case for NOC_DEST that writes data from partner primitive
  - **Leverage**: Existing golden_run tensor operations

- [x] **Task 7.3**: Implement NOC_FENCE case in golden_run
  - **Requirements**: REQ-GM-001
  - **Files**: SIMTemplate.py
  - **Description**: Add simple case for NOC_FENCE that returns True
  - **Leverage**: Existing golden_run structure

### Phase 8: Testing and Validation

- [ ] **Task 8.1**: Create unit test for primitive type separation
  - **Requirements**: REQ-IP-001
  - **Files**: test_noc_primitives.py (new)
  - **Description**: Test that instructions map to correct primitive types
  - **Leverage**: Existing test patterns

- [ ] **Task 8.2**: Create test for resource allocation correctness
  - **Requirements**: REQ-IP-002, REQ-IP-003
  - **Files**: test_noc_resources.py (new)
  - **Description**: Test that NOC_SRC only uses source resources, NOC_DEST only uses dest
  - **Leverage**: Existing resource testing patterns

- [ ] **Task 8.3**: Create integration test for single transfer
  - **Requirements**: REQ-DT-001, REQ-DT-004
  - **Files**: test_noc_transfer.py (new)
  - **Description**: Test complete NoC transfer with both execution orders (SRC-first and DEST-first scenarios)
  - **Test Cases**: Send noc_dest_drv then noc_src_drv; Send noc_src_drv then noc_dest_drv; Verify data integrity in both cases; Verify resource allocation correctness
  - **Leverage**: Existing simulation test framework

- [ ] **Task 8.4**: Create test for pairing timeout mechanism
  - **Requirements**: REQ-SY-003
  - **Files**: test_noc_timeout.py (new)
  - **Description**: Test that unpaired primitives timeout correctly
  - **Leverage**: None - new test

- [ ] **Task 8.5**: Create golden model validation test
  - **Requirements**: REQ-GM-001, REQ-GM-002
  - **Files**: test_noc_golden.py (new)
  - **Description**: Test that golden model correctly validates NoC transfers
  - **Leverage**: Existing golden model test patterns

- [ ] **Task 8.6**: Create execution order independence test
  - **Requirements**: REQ-SY-002
  - **Files**: test_noc_order_independence.py (new)
  - **Description**: Verify NoC transfer works correctly regardless of primitive arrival and execution order
  - **Test Cases**: Multiple transfers with mixed SRC/DEST arrival orders; Verify all transfers complete successfully; Verify no execution order dependencies
  - **Leverage**: Existing simulation test framework

## Task Dependencies

```mermaid
graph TD
    T1.1 --> T1.2
    T1.1 --> T1.3
    T1.2 --> T5.1
    
    T2.1 --> T2.2
    T2.2 --> T2.3
    T2.3 --> T2.4
    T2.3 --> T2.5
    T2.1 --> T2.6
    T2.6 --> T2.7
    
    T3.1 --> T5.2
    T3.1 --> T5.3
    T3.2 --> T2.5
    
    T4.1 --> T4.2
    T4.1 --> T4.3
    T4.1 --> T4.4
    
    T5.1 --> T5.2
    T5.1 --> T5.3
    
    T6.1 --> T6.3
    T6.1 --> T6.5
    T6.2 --> T6.4
    T6.2 --> T6.6
    T6.3 --> T6.4
    T6.1 --> T6.7
    T6.2 --> T6.7
    T6.7 --> T8.3
    T6.7 --> T8.6
    
    T1.3 --> T7.1
    T1.3 --> T7.2
    T1.3 --> T7.3
    
    T7.1 --> T8.5
    T7.2 --> T8.5
    T4.2 --> T8.2
    T4.3 --> T8.2
    T2.5 --> T8.3
    T6.6 --> T8.3
    T2.7 --> T8.4
```

## Execution Strategy

### Parallel Execution Opportunities
- Phase 1 tasks can run in parallel
- Tasks 2.1-2.4 and 2.6 can run in parallel
- Tasks 4.1-4.4 can run in parallel after 1.1
- Tasks 5.2 and 5.3 can run in parallel after 5.1
- All Phase 8 tests can run in parallel after their dependencies

### Critical Path
1. Task 1.1 → 1.2 → 5.1 → 5.2/5.3
2. Task 2.1 → 2.2 → 2.3 → 2.5
3. Task 6.1/6.2 → 6.7 → 8.3/8.6
4. Task 6.1 → 6.3 → 6.4

### Risk Areas
- Task 2.3: Order-independent pairing logic
- Task 6.7: Data synchronization between primitives
- Task 6.5/6.6: Memory access implementation
- Task 8.6: Order independence validation

## Success Metrics
- All 40 tasks completed (added Task 4.5, 6.7 and 8.6)
- All tests passing including order independence
- No resource allocation conflicts
- Successful NoC transfers regardless of primitive arrival order
- Golden model validation working
- Data synchronization working correctly

## MVP Task Priority

### Core MVP Tasks (Must Complete):
- **Phase 1**: Tasks 1.1, 1.2, 1.3 - Primitive type separation
- **Phase 2**: Tasks 2.1-2.5 - Basic pairing mechanism
- **Phase 3**: Task 3.1 - NoC settings enhancement
- **Phase 4**: Tasks 4.1-4.3, 4.5 - Resource allocation fix
- **Phase 6**: Tasks 6.1, 6.2, 6.5, 6.6, 6.7 - Data transfer implementation
- **Phase 8**: Tasks 8.2, 8.3 - Core functionality tests

### Optional Enhancement Tasks:
- Tasks 2.6, 2.7 - Timeout mechanism
- Task 4.4 - NOC_FENCE handling
- Tasks 6.3, 6.4 - NoC routing integration
- Phase 5 - TensorInfo handlers
- Phase 7 - Golden Model
- Tasks 8.1, 8.4, 8.5, 8.6 - Additional tests