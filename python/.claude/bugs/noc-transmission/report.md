# Bug Report: NOC Transmission

## Bug Summary
NOC transmission between NPU cores is not establishing proper SRC/DEST pairing for data transfer.

## Environment
- Project: Python MOSIM Workspace / Nuclei Project
- Component: NOC (Network-on-Chip) primitive operations
- Test Location: NPU core communication (test_noc.c)
- Simulator: MOSIM with NPU golden model

## Bug Description
When attempting to transfer a tensor from NPU core 0's SCRATCHPAD0_ADDR to NPU core 1's SCRATCHPAD0_ADDR using NOC primitives, the transmission does not complete successfully. Log analysis indicates that the SRC and DEST are not being properly paired to enable the actual NOC data transfer. The NOC controller receives both SRC and DEST primitives but fails to match them for data movement.

## Reproduction Steps
1. Configure tensor with dimensions 256x32x1
2. Set NPU mask to 0x3 (enabling cores 0 and 1)
3. Call `noc_primitive_cfg` with tensor and mask
4. Execute `noc_primitive_src_drv` from core 0
5. Execute `noc_primitive_dest_drv` to core 1
6. Observe logs showing no successful pairing

## Expected Behavior
- SR<PERSON> and DEST should be paired in the NOC controller
- Data should transfer from core 0 SPAD to core 1 SPAD
- Logs should show successful transmission completion

## Actual Behavior
- No SRC/DEST pairing occurs
- No data transfer takes place
- Logs indicate missing pairing mechanism

## Test Code
```c
Tensor shape_tensor = (Tensor){
    .base_addr      = -1,
    .dim0           = 256,
    .dim1           = 32,
    .dim2           = 1,
    .byte_stride1_u = 32 * 16, 
    .byte_stride2_u = 32 * 512, 
    .width          = WIDTH_16,
    .type           = TYPE_BF 
};

int npu_mask[MAX_MASK] = {0x3, 0, 0, 0}; 
noc_primitive_cfg(&shape_tensor, npu_mask);

noc_primitive_src_drv(SCRATCHPAD0_ADDR, 0x0001, (int[]){0x1, 0, 0, 0});
noc_primitive_dest_drv(SCRATCHPAD0_ADDR, 0x0000, (int[]){0x2, 0, 0, 0});
```

## Relevant Logs
- `logs/npu_demo_output.log`
- `logs/mosim_output.log`

## Impact
- Severity: High
- Blocks inter-core communication functionality
- Affects NOC-based tensor operations

## Log Analysis

From npu_demo_output.log:
```
[NoC] Handling PrimName.NOC_SRC primitive 2
      Source: Group None ID None
      Dest: Group [0, 0, 0, 0] ID 1
[NoC] No matching pair found, added to pending list (size: 1)

[NoC] Handling PrimName.NOC_DEST primitive 4
      Source: Group [0, 0, 0, 0] ID 0
      Dest: Group None ID None
[NoC] No matching pair found, added to pending list (size: 2)
```

The logs show:
1. NOC_SRC primitive is received with dest_idx=1 (targeting core 1)
2. NOC_DEST primitive is received with src_idx=0 (expecting from core 0)
3. Both primitives are added to pending list but never paired

## Root Cause Analysis

The issue appears to be a mismatch in the group/core identification:
- `noc_primitive_src_drv` is called with dest mask `{0x1, 0, 0, 0}` (group 0, core 0)
- `noc_primitive_dest_drv` is called with src mask `{0x2, 0, 0, 0}` (group 0, core 1)
- But the actual source is executing from core 0 and destination from core 1

The pairing mechanism expects matching source/destination IDs but receives:
- SRC: Source=None, Dest=ID 1
- DEST: Source=ID 0, Dest=None

## Additional Context
- NOC spec implementation is complete
- Individual SRC and DEST drivers execute without error
- Issue appears to be in the pairing/coordination mechanism
- The group mask configuration (0x3) correctly enables cores 0 and 1

## Attachments
- Log files: `logs/npu_demo_output.log`, `logs/mosim_output.log`
- Test case: `/data/users/jxchen/nuclei-project/application/tests/test_noc.c`
- Implementation: `primitive_single.c`, `nice_inst_single.h`

---
*Created: 2025-08-04*