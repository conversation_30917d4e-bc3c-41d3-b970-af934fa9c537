# Bug Analysis: NOC Transmission

## Analysis Status
- [x] Root cause identified
- [x] Code paths traced
- [x] Fix approach determined

## Investigation Summary

### 1. Log Analysis Findings

From the execution logs, I identified the following sequence:
1. **NOC_SRC primitive (Pid: 2)** executed on core 0 (Group 0, Mask [True, False, False, False])
   - Instruction: `noc_src_drv(20e7b00b, 0, 1)` 
   - Parameters: base_addr=0, dest_idx=1
   - <PERSON><PERSON> output: "Source: Group None ID None, Dest: Group [0, 0, 0, 0] ID 1"

2. **NOC_DEST primitive (Pid: 4)** executed on core 1 (Group 0, Mask [False, True, False, False])
   - Instruction: `noc_dest_drv(22e7b00b, 0, 0)`
   - Parameters: base_addr=0, src_idx=0
   - <PERSON><PERSON> output: "Source: Group [0, 0, 0, 0] ID 0, Dest: Group None ID None"

3. Both primitives added to pending list but never paired

### 2. Code Flow Analysis

#### Test Code Execution:
```c
// test_noc.c
noc_primitive_src_drv(SCRATCHPAD0_ADDR, 0x0001, (int[]){0x1, 0, 0, 0});
noc_primitive_dest_drv(SCRATCHPAD0_ADDR, 0x0000, (int[]){0x2, 0, 0, 0});
```

#### primitive_single.c Implementation:
- `noc_primitive_src_drv` loops through npu_mask and executes on core 0 (mask 0x1)
- `noc_primitive_dest_drv` loops through npu_mask and executes on core 1 (mask 0x2)
- Each calls low-level `noc_src_drv` / `noc_dest_drv` assembly instructions

#### Golden Model Processing:
- NOC primitives are received by the golden model
- The model attempts to pair based on matching criteria in `is_matching_pair()`
- Pairing requires: same src_group, src_id, dst_group, dst_id between SRC and DEST primitives

## Root Cause

The root cause is a **flawed pairing logic** in the NOC primitive matching:

1. **Current Design Intention**
   - `noc_src_drv(addr, dest_idx)`: Executed on source core, specifies destination core
   - `noc_dest_drv(addr, src_idx)`: Executed on destination core, specifies source core
   - This design is logically sound - each primitive knows its counterpart

2. **Actual Implementation Problem**
   - NOC_SRC fills: `dst_id=1, dst_group=[0,0,0,0]` but leaves `src_id=None, src_group=None`
   - NOC_DEST fills: `src_id=0, src_group=[0,0,0,0]` but leaves `dst_id=None, dst_group=None`
   - This is correct per the `fill_nocinfo()` function in tensorinfo.py

3. **The Matching Logic Flaw**
   ```python
   def is_matching_pair(self, prim1, prim2):
       return (prim1.noc_settings.src_group == prim2.noc_settings.src_group and
               prim1.noc_settings.src_id == prim2.noc_settings.src_id and
               prim1.noc_settings.dst_group == prim2.noc_settings.dst_group and
               prim1.noc_settings.dst_id == prim2.noc_settings.dst_id and ...)
   ```
   - Requires ALL four fields to match exactly
   - But SRC has src=None and DEST has dst=None
   - None != [0,0,0,0], so they never match!

4. **Why This Is Wrong**
   - The matching should check: SRC's dst_id == DEST's executing core AND DEST's src_id == SRC's executing core
   - Current logic assumes both primitives have complete src/dst information, which they don't

## Affected Components

1. **Golden Model (Python)**
   - NOC primitive decoder/handler
   - NOC settings population
   - Pairing mechanism in SIMTop.py

2. **Test Implementation** 
   - Current test uses separate masks for SRC/DEST
   - May need to execute both primitives on both cores

3. **Primitive Library**
   - Implementation seems correct but usage pattern may be wrong

## Fix Approach

### Option 1: Fix the Matching Logic (Recommended)
- Modify `is_matching_pair()` to correctly match complementary primitives
- SRC primitive with dst_id=X should match DEST primitive with src_id=Y where:
  - Y is the core executing the SRC primitive
  - X is the core executing the DEST primitive
- Need to identify executing cores from group mask information

### Option 2: Fill Missing Information
- When NOC_SRC executes, fill its src_id/src_group based on executing core
- When NOC_DEST executes, fill its dst_id/dst_group based on executing core
- Keep current matching logic unchanged
- This requires modifying the primitive creation/filling process

### Option 3: Simplified Matching
- Match based only on the information each primitive has:
  - SRC has dst_id, DEST has src_id
  - Check if they point to each other's executing cores
- Remove the requirement for all fields to match

## Recommended Solution

**Implement Option 1** - Fix the matching logic:

```python
def is_matching_pair(self, prim1, prim2):
    """Check if two primitives form a valid NoC transfer pair"""
    if prim1.type == PrimName.NOC_SRC and prim2.type == PrimName.NOC_DEST:
        # SRC knows destination, DEST knows source
        # Check if SRC's destination matches DEST's executing core
        # AND DEST's source matches SRC's executing core
        return (prim1.noc_settings.dst_id == <prim2's executing core id> and
                prim2.noc_settings.src_id == <prim1's executing core id>)
    elif prim1.type == PrimName.NOC_DEST and prim2.type == PrimName.NOC_SRC:
        # Reverse case
        return (prim2.noc_settings.dst_id == <prim1's executing core id> and
                prim1.noc_settings.src_id == <prim2's executing core id>)
    return False
```

The key challenge is determining each primitive's executing core from the available information.

## Testing Strategy

1. Verify simple 2-core transfer works after fix
2. Test multi-core scenarios (broadcast, gather)
3. Ensure no regression in existing NOC tests
4. Add unit tests for pairing mechanism

## Prevention

1. Add assertions to verify NOC settings are complete
2. Improve debug logging to show executing core
3. Document correct NOC primitive usage patterns
4. Add integration tests for core-to-core transfers

---
*Analysis completed: 2025-08-04*